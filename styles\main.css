/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    overflow: hidden;
    user-select: none;
}

/* Game Container */
#gameContainer {
    width: 100vw;
    height: 100vh;
    position: relative;
}

/* Screen Management */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.screen.active {
    display: flex;
    opacity: 1;
}

/* Home Screen */
#homeScreen {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    justify-content: center;
    align-items: center;
}

.home-content {
    text-align: center;
    max-width: 600px;
    padding: 2rem;
}

.game-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 2rem;
    background: linear-gradient(45deg, #00d4ff, #7b2cbf);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.menu-btn {
    padding: 1rem 2rem;
    font-size: 1.2rem;
    font-weight: 600;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.menu-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
}

.menu-btn.primary {
    background: linear-gradient(45deg, #00d4ff, #7b2cbf);
    border: none;
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.4);
}

.menu-btn.primary:hover {
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.6);
    transform: translateY(-3px);
}

.game-stats {
    display: flex;
    justify-content: space-around;
    margin-top: 2rem;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

/* Game Screen */
#gameScreen {
    background: #0a0a0a;
}

#gameCanvas {
    display: block;
    background: #0a0a0a;
}

/* UI Elements */
#gameUI {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

#gameUI > * {
    pointer-events: auto;
}

/* Top HUD */
#topHUD {
    position: absolute;
    top: 20px;
    left: 20px;
    display: flex;
    gap: 20px;
    align-items: center;
}

.health-bar, .hunger-bar {
    width: 150px;
    height: 20px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.health-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff4444, #ff6666);
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(255, 68, 68, 0.5);
}

.hunger-fill {
    height: 100%;
    background: linear-gradient(90deg, #ffaa44, #ffcc66);
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(255, 170, 68, 0.5);
}

.health-text, .hunger-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.level-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-size: 14px;
    color: #00d4ff;
    text-shadow: 0 0 5px rgba(0, 212, 255, 0.5);
}

/* Inventory Panel */
.inventory-panel {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 12px;
    padding: 20px;
    min-width: 400px;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    display: none;
    backdrop-filter: blur(10px);
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.2);
}

.inventory-panel.active {
    display: block;
}

.inventory-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.inventory-header h3 {
    color: #00d4ff;
    font-size: 1.5rem;
}

.close-btn {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ff4444;
}

.inventory-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
    margin-bottom: 20px;
}

.inventory-slot {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.inventory-slot:hover {
    background: rgba(0, 212, 255, 0.2);
    border-color: #00d4ff;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.inventory-slot.has-item {
    background: rgba(0, 212, 255, 0.1);
    border-color: #00d4ff;
}

.item-count {
    position: absolute;
    bottom: 2px;
    right: 2px;
    font-size: 10px;
    color: #ffffff;
    background: rgba(0, 0, 0, 0.8);
    padding: 1px 3px;
    border-radius: 3px;
}

/* Crafting Section */
.crafting-section {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 20px;
}

.crafting-section h4 {
    color: #00d4ff;
    margin-bottom: 15px;
}

.crafting-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.crafting-recipe {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.crafting-recipe:hover {
    background: rgba(0, 212, 255, 0.1);
    border-color: #00d4ff;
}

.crafting-recipe.can-craft {
    border-color: #44ff44;
    background: rgba(68, 255, 68, 0.1);
}

/* Building Panel */
.building-panel {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 12px;
    padding: 20px;
    min-width: 300px;
    display: none;
    backdrop-filter: blur(10px);
}

.building-panel.active {
    display: block;
}

.building-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.building-header h3 {
    color: #00d4ff;
    font-size: 1.5rem;
}

.building-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
}

.building-option {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.building-option:hover {
    background: rgba(0, 212, 255, 0.1);
    border-color: #00d4ff;
    transform: translateY(-2px);
}

.building-option.can-build {
    border-color: #44ff44;
    background: rgba(68, 255, 68, 0.1);
}

/* Minimap */
.minimap {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 200px;
    height: 200px;
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 8px;
    overflow: hidden;
}

#minimapCanvas {
    width: 100%;
    height: 100%;
}

/* Chat Panel */
.chat-panel {
    position: absolute;
    bottom: 20px;
    left: 20px;
    width: 300px;
    height: 200px;
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    backdrop-filter: blur(10px);
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    font-size: 12px;
    line-height: 1.4;
}

.chat-message {
    margin-bottom: 5px;
    padding: 3px 0;
}

.chat-message.system {
    color: #ffaa44;
    font-style: italic;
}

.chat-message.player {
    color: #00d4ff;
}

.chat-message.other {
    color: #ffffff;
}

.chat-input-container {
    display: flex;
    padding: 10px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

#chatInput {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    padding: 5px 10px;
    color: #ffffff;
    font-size: 12px;
}

#chatInput:focus {
    outline: none;
    border-color: #00d4ff;
    box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
}

#sendChat {
    margin-left: 5px;
    padding: 5px 10px;
    background: rgba(0, 212, 255, 0.2);
    border: 1px solid #00d4ff;
    border-radius: 4px;
    color: #ffffff;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

#sendChat:hover {
    background: rgba(0, 212, 255, 0.3);
}

/* Settings Panel */
.settings-panel {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 12px;
    padding: 20px;
    min-width: 300px;
    display: none;
    backdrop-filter: blur(10px);
}

.settings-panel.active {
    display: block;
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.settings-header h3 {
    color: #00d4ff;
    font-size: 1.5rem;
}

.setting-group {
    margin-bottom: 15px;
}

.setting-group label {
    display: block;
    margin-bottom: 5px;
    color: #ffffff;
    font-size: 14px;
}

.setting-group input[type="range"] {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    outline: none;
}

.setting-group input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 16px;
    height: 16px;
    background: #00d4ff;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.setting-group select {
    width: 100%;
    padding: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    color: #ffffff;
    font-size: 14px;
}

.setting-group select:focus {
    outline: none;
    border-color: #00d4ff;
}

.save-btn {
    width: 100%;
    padding: 10px;
    background: linear-gradient(45deg, #00d4ff, #7b2cbf);
    border: none;
    border-radius: 6px;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.save-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
}

/* Game Controls */
#gameControls {
    position: absolute;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
}

.control-btn {
    width: 50px;
    height: 50px;
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.control-btn:hover {
    background: rgba(0, 212, 255, 0.2);
    border-color: #00d4ff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

/* Loading Screen */
#loadingScreen {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    justify-content: center;
    align-items: center;
}

.loading-content {
    text-align: center;
    max-width: 400px;
}

.loading-content h2 {
    color: #00d4ff;
    margin-bottom: 20px;
    font-size: 2rem;
}

.loading-bar {
    width: 300px;
    height: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    overflow: hidden;
    margin: 20px 0;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.loading-fill {
    height: 100%;
    background: linear-gradient(90deg, #00d4ff, #7b2cbf);
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

#loadingText {
    color: #ffffff;
    font-size: 14px;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-title {
        font-size: 2.5rem;
    }
    
    .menu-buttons {
        gap: 0.5rem;
    }
    
    .menu-btn {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }
    
    #topHUD {
        flex-direction: column;
        gap: 10px;
    }
    
    .health-bar, .hunger-bar {
        width: 120px;
        height: 15px;
    }
    
    .minimap {
        width: 150px;
        height: 150px;
    }
    
    .chat-panel {
        width: 250px;
        height: 150px;
    }
    
    .inventory-panel {
        min-width: 300px;
        max-width: 90vw;
    }
    
    .inventory-grid {
        grid-template-columns: repeat(6, 1fr);
    }
}

/* Animations */
@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px rgba(0, 212, 255, 0.3); }
    50% { box-shadow: 0 0 20px rgba(0, 212, 255, 0.6); }
}

.glow {
    animation: glow 2s ease-in-out infinite;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 212, 255, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 212, 255, 0.7);
} 
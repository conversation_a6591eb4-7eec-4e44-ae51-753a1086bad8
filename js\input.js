// Input System
class InputManager {
    constructor() {
        this.keys = {};
        this.mouse = {
            x: 0,
            y: 0,
            left: false,
            right: false,
            middle: false
        };
        this.touch = {
            x: 0,
            y: 0,
            active: false
        };
        this.gameCanvas = null;
        this.worldOffset = { x: 0, y: 0 };
        this.zoom = 1.0;
        
        this.setupEventListeners();
    }

    // Initialize input system
    init(canvas) {
        this.gameCanvas = canvas;
        this.updateMousePosition = this.updateMousePosition.bind(this);
        this.updateTouchPosition = this.updateTouchPosition.bind(this);
    }

    // Setup all event listeners
    setupEventListeners() {
        // Keyboard events
        document.addEventListener('keydown', (e) => {
            this.keys[e.keyCode] = true;
            this.handleKeyDown(e);
        });

        document.addEventListener('keyup', (e) => {
            this.keys[e.keyCode] = false;
            this.handleKeyUp(e);
        });

        // Mouse events
        document.addEventListener('mousedown', (e) => {
            this.handleMouseDown(e);
        });

        document.addEventListener('mouseup', (e) => {
            this.handleMouseUp(e);
        });

        document.addEventListener('mousemove', (e) => {
            this.updateMousePosition(e);
        });

        // Touch events for mobile
        document.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.handleTouchStart(e);
        });

        document.addEventListener('touchend', (e) => {
            e.preventDefault();
            this.handleTouchEnd(e);
        });

        document.addEventListener('touchmove', (e) => {
            e.preventDefault();
            this.handleTouchMove(e);
        });

        // Prevent context menu on right click
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });

        // Handle window focus/blur
        window.addEventListener('blur', () => {
            this.clearAllInputs();
        });

        // Handle visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.clearAllInputs();
            }
        });
    }

    // Handle key down events
    handleKeyDown(e) {
        // Prevent default for game keys
        if (this.isGameKey(e.keyCode)) {
            e.preventDefault();
        }

        // Handle specific key actions
        switch (e.keyCode) {
            case KEYS.ESC:
                this.handleEscape();
                break;
            case KEYS.I:
                this.handleInventory();
                break;
            case KEYS.B:
                this.handleBuilding();
                break;
            case KEYS.ENTER:
                this.handleEnter();
                break;
            case KEYS.TAB:
                e.preventDefault();
                this.handleTab();
                break;
        }
    }

    // Handle key up events
    handleKeyUp(e) {
        // Handle specific key release actions
        switch (e.keyCode) {
            case KEYS.SHIFT:
                this.handleShiftRelease();
                break;
        }
    }

    // Handle mouse down events
    handleMouseDown(e) {
        this.updateMousePosition(e);
        
        switch (e.button) {
            case 0: // Left click
                this.mouse.left = true;
                this.handleLeftClick(e);
                break;
            case 1: // Middle click
                this.mouse.middle = true;
                this.handleMiddleClick(e);
                break;
            case 2: // Right click
                this.mouse.right = true;
                this.handleRightClick(e);
                break;
        }
    }

    // Handle mouse up events
    handleMouseUp(e) {
        this.updateMousePosition(e);
        
        switch (e.button) {
            case 0:
                this.mouse.left = false;
                break;
            case 1:
                this.mouse.middle = false;
                break;
            case 2:
                this.mouse.right = false;
                break;
        }
    }

    // Handle touch start events
    handleTouchStart(e) {
        if (e.touches.length > 0) {
            const touch = e.touches[0];
            this.touch.x = touch.clientX;
            this.touch.y = touch.clientY;
            this.touch.active = true;
            this.handleTouchAction('start', touch);
        }
    }

    // Handle touch end events
    handleTouchEnd(e) {
        this.touch.active = false;
        this.handleTouchAction('end');
    }

    // Handle touch move events
    handleTouchMove(e) {
        if (e.touches.length > 0) {
            const touch = e.touches[0];
            this.touch.x = touch.clientX;
            this.touch.y = touch.clientY;
            this.handleTouchAction('move', touch);
        }
    }

    // Update mouse position
    updateMousePosition(e) {
        if (this.gameCanvas) {
            const rect = this.gameCanvas.getBoundingClientRect();
            this.mouse.x = e.clientX - rect.left;
            this.mouse.y = e.clientY - rect.top;
        } else {
            this.mouse.x = e.clientX;
            this.mouse.y = e.clientY;
        }
    }

    // Update touch position
    updateTouchPosition(e) {
        if (e.touches.length > 0) {
            const touch = e.touches[0];
            if (this.gameCanvas) {
                const rect = this.gameCanvas.getBoundingClientRect();
                this.touch.x = touch.clientX - rect.left;
                this.touch.y = touch.clientY - rect.top;
            } else {
                this.touch.x = touch.clientX;
                this.touch.y = touch.clientY;
            }
        }
    }

    // Convert screen coordinates to world coordinates
    screenToWorld(screenX, screenY) {
        if (!this.gameCanvas) return { x: screenX, y: screenY };
        
        const rect = this.gameCanvas.getBoundingClientRect();
        const canvasX = screenX - rect.left;
        const canvasY = screenY - rect.top;
        
        return {
            x: (canvasX / this.zoom) + this.worldOffset.x,
            y: (canvasY / this.zoom) + this.worldOffset.y
        };
    }

    // Convert world coordinates to screen coordinates
    worldToScreen(worldX, worldY) {
        if (!this.gameCanvas) return { x: worldX, y: worldY };
        
        const rect = this.gameCanvas.getBoundingClientRect();
        const canvasX = (worldX - this.worldOffset.x) * this.zoom;
        const canvasY = (worldY - this.worldOffset.y) * this.zoom;
        
        return {
            x: canvasX + rect.left,
            y: canvasY + rect.top
        };
    }

    // Get mouse position in world coordinates
    getMouseWorldPosition() {
        return this.screenToWorld(this.mouse.x, this.mouse.y);
    }

    // Get touch position in world coordinates
    getTouchWorldPosition() {
        return this.screenToWorld(this.touch.x, this.touch.y);
    }

    // Check if a key is pressed
    isKeyPressed(keyCode) {
        return this.keys[keyCode] === true;
    }

    // Check if any movement key is pressed
    isMovementKeyPressed() {
        return this.isKeyPressed(KEYS.W) || 
               this.isKeyPressed(KEYS.A) || 
               this.isKeyPressed(KEYS.S) || 
               this.isKeyPressed(KEYS.D);
    }

    // Get movement direction
    getMovementDirection() {
        let dx = 0;
        let dy = 0;
        
        if (this.isKeyPressed(KEYS.W)) dy -= 1;
        if (this.isKeyPressed(KEYS.S)) dy += 1;
        if (this.isKeyPressed(KEYS.A)) dx -= 1;
        if (this.isKeyPressed(KEYS.D)) dx += 1;
        
        // Normalize diagonal movement
        if (dx !== 0 && dy !== 0) {
            const length = Math.sqrt(dx * dx + dy * dy);
            dx /= length;
            dy /= length;
        }
        
        return { x: dx, y: dy };
    }

    // Check if mouse button is pressed
    isMousePressed(button) {
        switch (button) {
            case 'left': return this.mouse.left;
            case 'right': return this.mouse.right;
            case 'middle': return this.mouse.middle;
            default: return false;
        }
    }

    // Check if touch is active
    isTouchActive() {
        return this.touch.active;
    }

    // Check if a key is a game key
    isGameKey(keyCode) {
        return [
            KEYS.W, KEYS.A, KEYS.S, KEYS.D,
            KEYS.SPACE, KEYS.SHIFT, KEYS.CTRL,
            KEYS.ESC, KEYS.I, KEYS.B, KEYS.ENTER,
            KEYS.TAB
        ].includes(keyCode);
    }

    // Clear all input states
    clearAllInputs() {
        this.keys = {};
        this.mouse.left = false;
        this.mouse.right = false;
        this.mouse.middle = false;
        this.touch.active = false;
    }

    // Handle specific key actions
    handleEscape() {
        if (window.game) {
            game.togglePause();
        }
    }

    handleInventory() {
        if (window.game && game.ui) {
            game.ui.toggleInventory();
        }
    }

    handleBuilding() {
        if (window.game && game.ui) {
            game.ui.toggleBuilding();
        }
    }

    handleEnter() {
        if (window.game && game.chat) {
            game.chat.toggleChat();
        }
    }

    handleTab() {
        // Handle tab completion or other tab actions
        if (window.game && game.chat && game.chat.isActive()) {
            game.chat.handleTabCompletion();
        }
    }

    handleShiftRelease() {
        // Handle shift release actions
        if (window.game && game.player) {
            game.player.setSprinting(false);
        }
    }

    // Handle mouse click actions
    handleLeftClick(e) {
        if (window.game) {
            const worldPos = this.getMouseWorldPosition();
            game.handleLeftClick(worldPos.x, worldPos.y);
        }
    }

    handleRightClick(e) {
        if (window.game) {
            const worldPos = this.getMouseWorldPosition();
            game.handleRightClick(worldPos.x, worldPos.y);
        }
    }

    handleMiddleClick(e) {
        if (window.game) {
            const worldPos = this.getMouseWorldPosition();
            game.handleMiddleClick(worldPos.x, worldPos.y);
        }
    }

    // Handle touch actions
    handleTouchAction(action, touch = null) {
        if (!window.game) return;
        
        switch (action) {
            case 'start':
                if (touch) {
                    const worldPos = this.getTouchWorldPosition();
                    game.handleTouchStart(worldPos.x, worldPos.y);
                }
                break;
            case 'move':
                if (touch) {
                    const worldPos = this.getTouchWorldPosition();
                    game.handleTouchMove(worldPos.x, worldPos.y);
                }
                break;
            case 'end':
                game.handleTouchEnd();
                break;
        }
    }

    // Set world offset (for camera)
    setWorldOffset(x, y) {
        this.worldOffset.x = x;
        this.worldOffset.y = y;
    }

    // Set zoom level
    setZoom(zoom) {
        this.zoom = Utils.clamp(zoom, 0.1, 3.0);
    }

    // Get current zoom level
    getZoom() {
        return this.zoom;
    }

    // Handle mouse wheel for zoom
    handleMouseWheel(delta) {
        const zoomSpeed = 0.1;
        const newZoom = this.zoom + (delta > 0 ? zoomSpeed : -zoomSpeed);
        this.setZoom(newZoom);
        
        if (window.game && game.camera) {
            game.camera.setZoom(this.zoom);
        }
    }

    // Check if input is blocked (e.g., by UI)
    isInputBlocked() {
        // Check if any UI panel is active
        const activePanels = document.querySelectorAll('.inventory-panel.active, .building-panel.active, .settings-panel.active');
        return activePanels.length > 0;
    }

    // Get input priority (for handling overlapping UI)
    getInputPriority() {
        const priorities = {
            'settings-panel': 3,
            'inventory-panel': 2,
            'building-panel': 2,
            'chat-panel': 1
        };
        
        let maxPriority = 0;
        for (const [className, priority] of Object.entries(priorities)) {
            if (document.querySelector(`.${className}.active`)) {
                maxPriority = Math.max(maxPriority, priority);
            }
        }
        
        return maxPriority;
    }

    // Update method (called each frame)
    update() {
        // Handle continuous input (like holding keys)
        if (this.isKeyPressed(KEYS.SHIFT) && window.game && game.player) {
            game.player.setSprinting(true);
        }
        
        // Handle mouse/touch movement
        if (this.mouse.left && window.game) {
            const worldPos = this.getMouseWorldPosition();
            game.handleMouseDrag(worldPos.x, worldPos.y);
        }
        
        if (this.touch.active && window.game) {
            const worldPos = this.getTouchWorldPosition();
            game.handleTouchDrag(worldPos.x, worldPos.y);
        }
    }

    // Destroy event listeners
    destroy() {
        // Remove event listeners if needed
        document.removeEventListener('keydown', this.handleKeyDown);
        document.removeEventListener('keyup', this.handleKeyUp);
        document.removeEventListener('mousedown', this.handleMouseDown);
        document.removeEventListener('mouseup', this.handleMouseUp);
        document.removeEventListener('mousemove', this.updateMousePosition);
    }
}

// Global input manager instance
let inputManager; 
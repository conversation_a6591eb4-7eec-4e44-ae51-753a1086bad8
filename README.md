# Survival.io - Multiplayer Survival Game

A complete web-based survival game inspired by moomoo.io and sploop.io, featuring resource gathering, crafting, building, combat, and multiplayer elements.

## 🎮 Features

### Core Gameplay
- **Survival Mechanics**: Health, hunger, thirst, and stamina systems
- **Resource Gathering**: Harvest trees, rocks, berry bushes, and water sources
- **Crafting System**: Create tools, weapons, armor, and buildings
- **Building System**: Construct walls, foundations, turrets, and storage
- **Combat System**: Melee and ranged combat with various weapons
- **AI NPCs**: Animals and enemies with intelligent behavior
- **Experience System**: Level up and improve your character

### User Interface
- **Home Screen**: Beautiful landing page with game statistics
- **Inventory System**: 32-slot inventory with item management
- **Crafting Interface**: Visual crafting recipes with material requirements
- **Building Menu**: Easy-to-use building placement system
- **Minimap**: Real-time world overview
- **Chat System**: Player communication
- **Settings Panel**: Audio and graphics customization

### Technical Features
- **Responsive Design**: Works on desktop and mobile devices
- **Audio System**: Dynamic sound effects and ambient music
- **Particle Effects**: Visual feedback for actions and combat
- **Smooth Animations**: Fluid movement and UI transitions
- **Performance Optimized**: Efficient rendering and update loops

## 🚀 How to Play

### Getting Started
1. Open `index.html` in a modern web browser
2. Click "Play Game" to start
3. Use WASD keys to move your character
4. Left-click to interact with resources and objects
5. Press 'I' to open inventory
6. Press 'B' to open building menu

### Controls
- **WASD**: Move character
- **Left Click**: Interact, attack, harvest
- **Right Click**: Secondary actions
- **I**: Open/close inventory
- **B**: Open/close building menu
- **ESC**: Pause game
- **Shift**: Sprint (consumes stamina)

### Gameplay Tips
1. **Start by gathering resources**: Trees provide wood, rocks provide stone
2. **Craft basic tools**: Axe for trees, pickaxe for rocks
3. **Build a foundation**: Create a base to store items
4. **Craft weapons**: Defend yourself from aggressive NPCs
5. **Manage your stats**: Keep hunger and thirst above 50% for health regeneration
6. **Level up**: Gain experience by harvesting and defeating enemies

## 🏗️ Building System

### Available Buildings
- **Wall**: Basic defensive structure
- **Foundation**: Base for larger structures
- **Turret**: Automated defense system
- **Campfire**: Cooking and light source
- **Storage**: Item storage with capacity

### Building Tips
- Buildings require materials from your inventory
- Some buildings can be upgraded for better performance
- Turrets automatically target nearby enemies
- Storage buildings can hold additional items

## ⚔️ Combat System

### Weapons
- **Sword**: Melee weapon with high damage
- **Bow**: Ranged weapon with arrows
- **Axe**: Tool that can be used as weapon
- **Pickaxe**: Mining tool with combat capability

### Combat Tips
- Different weapons have different ranges and damage
- Some weapons are better against specific enemies
- Use terrain and buildings for tactical advantage
- Turrets provide automated defense

## 🎯 Crafting System

### Crafting Recipes
- **Axe**: 3 Wood + 2 Stone
- **Pickaxe**: 2 Wood + 3 Stone
- **Sword**: 2 Wood + 4 Stone
- **Bow**: 4 Wood + 1 Stone
- **Arrow**: 1 Wood + 1 Stone (creates 5 arrows)
- **Armor**: 3 Wood + 5 Stone
- **Wall**: 2 Wood + 3 Stone
- **Foundation**: 4 Wood + 6 Stone
- **Turret**: 8 Wood + 12 Stone + 1 Sword

### Crafting Tips
- Ensure you have all required materials before crafting
- Some items require tools to craft efficiently
- Higher-level items provide better performance
- Crafting takes time - be patient!

## 🌍 World System

### Resources
- **Trees**: Provide wood and occasionally food
- **Rocks**: Provide stone and occasionally wood
- **Berry Bushes**: Provide food
- **Water Sources**: Provide water (infinite)

### NPCs
- **Deer**: Passive animals that provide food
- **Rabbits**: Fast animals that provide food
- **Wolves**: Aggressive predators
- **Bears**: Powerful enemies with high health

### World Features
- **Dynamic Spawning**: Resources and NPCs spawn over time
- **Respawn System**: Harvested resources respawn after time
- **Terrain**: Various terrain types affect gameplay
- **Day/Night Cycle**: Affects visibility and NPC behavior

## 🎨 Customization

### Settings
- **Music Volume**: Adjust background music
- **SFX Volume**: Adjust sound effects
- **Graphics Quality**: Low, Medium, High
- **Audio Settings**: Save preferences locally

### Visual Features
- **Dark Theme**: Moody, atmospheric design
- **Glowing Effects**: Dynamic lighting and particles
- **Smooth Animations**: Fluid movement and transitions
- **Responsive UI**: Adapts to different screen sizes

## 🔧 Technical Details

### File Structure
```
moomoo.io/
├── index.html              # Main HTML file
├── styles/
│   └── main.css           # Game styles
├── js/
│   ├── utils.js           # Utility functions
│   ├── constants.js       # Game constants
│   ├── audio.js           # Audio system
│   ├── input.js           # Input handling
│   ├── entities/
│   │   ├── entity.js      # Base entity class
│   │   ├── player.js      # Player class
│   │   ├── npc.js         # NPC class
│   │   ├── resource.js    # Resource class
│   │   ├── building.js    # Building class
│   │   └── projectile.js  # Projectile class
│   ├── world/
│   │   ├── world.js       # World management
│   │   └── minimap.js     # Minimap system
│   ├── ui/
│   │   ├── inventory.js   # Inventory UI
│   │   ├── building.js    # Building UI
│   │   ├── chat.js        # Chat system
│   │   └── settings.js    # Settings UI
│   ├── game.js            # Main game logic
│   └── main.js            # Game initialization
└── README.md              # This file
```

### Browser Requirements
- Modern web browser with HTML5 Canvas support
- JavaScript enabled
- Web Audio API support (for sound effects)
- Local storage support (for settings)

### Performance
- Optimized for 60 FPS gameplay
- Efficient entity management
- Culling for off-screen objects
- Particle system with automatic cleanup

## 🎮 Game Modes

### Single Player
- Practice mode with unlimited resources
- Learn crafting and building mechanics
- Test different strategies

### Multiplayer (Future)
- Real-time multiplayer gameplay
- Player vs Player combat
- Team-based building
- Global leaderboards

## 🚀 Development

### Running the Game
1. Download all files to a local directory
2. Open `index.html` in a web browser
3. No server required - runs entirely in the browser

### Modifying the Game
- Edit `js/constants.js` to modify game balance
- Modify `js/entities/` files to change entity behavior
- Update `styles/main.css` for visual changes
- Add new features in `js/game.js`

### Adding Content
- New items: Add to `ITEMS` in constants.js
- New resources: Add to `RESOURCES` in constants.js
- New buildings: Add to `BUILDINGS` in constants.js
- New NPCs: Add to `NPCS` in constants.js

## 🎯 Future Enhancements

### Planned Features
- **Multiplayer Support**: Real-time multiplayer gameplay
- **More Biomes**: Different environments with unique resources
- **Advanced Crafting**: Complex recipes and item combinations
- **Clan System**: Team-based gameplay
- **Achievements**: Goals and progression tracking
- **Trading System**: Player-to-player item exchange
- **Weather System**: Dynamic weather affecting gameplay
- **Seasons**: Changing seasons with different resources

### Technical Improvements
- **WebGL Rendering**: Hardware-accelerated graphics
- **WebSocket Networking**: Real-time multiplayer
- **Procedural Generation**: Infinite world generation
- **Mobile Optimization**: Touch controls and mobile UI
- **Progressive Web App**: Installable game app

## 📝 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions are welcome! Feel free to:
- Report bugs
- Suggest new features
- Submit code improvements
- Create artwork or sound effects

## 🎮 Enjoy the Game!

Survival.io offers a complete survival gaming experience with deep mechanics, beautiful visuals, and engaging gameplay. Whether you're gathering resources, building bases, or fighting enemies, there's always something to do in this dynamic world.

**Happy surviving!** 🌟 
// Game Constants
const GAME_CONFIG = {
    CANVAS_WIDTH: 1920,
    CANVAS_HEIGHT: 1080,
    TILE_SIZE: 32,
    PLAYER_SPEED: 3,
    PLAYER_SIZE: 24,
    WORLD_SIZE: 2000,
    RESOURCE_SPAWN_RATE: 0.01,
    NPC_SPAWN_RATE: 0.005,
    MAX_PLAYERS: 50,
    MAX_RESOURCES: 200,
    MAX_NPCS: 30
};

// Item Types
const ITEM_TYPES = {
    WOOD: 'wood',
    STONE: 'stone',
    FOOD: 'food',
    WATER: 'water',
    TOOL: 'tool',
    WEAPON: 'weapon',
    ARMOR: 'armor',
    BUILDING: 'building',
    RESOURCE: 'resource'
};

// Item Definitions
const ITEMS = {
    WOOD: {
        id: 'wood',
        name: 'Wood',
        type: ITEM_TYPES.RESOURCE,
        stackable: true,
        maxStack: 99,
        icon: '🪵',
        color: '#8B4513',
        value: 1
    },
    STONE: {
        id: 'stone',
        name: 'Stone',
        type: ITEM_TYPES.RESOURCE,
        stackable: true,
        maxStack: 99,
        icon: '🪨',
        color: '#696969',
        value: 2
    },
    FOOD: {
        id: 'food',
        name: 'Food',
        type: ITEM_TYPES.FOOD,
        stackable: true,
        maxStack: 99,
        icon: '🍎',
        color: '#FF6B6B',
        value: 5,
        nutrition: 20
    },
    WATER: {
        id: 'water',
        name: 'Water',
        type: ITEM_TYPES.FOOD,
        stackable: true,
        maxStack: 99,
        icon: '💧',
        color: '#4ECDC4',
        value: 3,
        hydration: 15
    },
    AXE: {
        id: 'axe',
        name: 'Axe',
        type: ITEM_TYPES.TOOL,
        stackable: false,
        icon: '🪓',
        color: '#8B4513',
        value: 10,
        damage: 15,
        durability: 100
    },
    PICKAXE: {
        id: 'pickaxe',
        name: 'Pickaxe',
        type: ITEM_TYPES.TOOL,
        stackable: false,
        icon: '⛏️',
        color: '#696969',
        value: 12,
        damage: 12,
        durability: 100
    },
    SWORD: {
        id: 'sword',
        name: 'Sword',
        type: ITEM_TYPES.WEAPON,
        stackable: false,
        icon: '⚔️',
        color: '#C0C0C0',
        value: 25,
        damage: 25,
        durability: 100
    },
    BOW: {
        id: 'bow',
        name: 'Bow',
        type: ITEM_TYPES.WEAPON,
        stackable: false,
        icon: '🏹',
        color: '#8B4513',
        value: 30,
        damage: 20,
        durability: 100,
        range: 200
    },
    ARROW: {
        id: 'arrow',
        name: 'Arrow',
        type: ITEM_TYPES.WEAPON,
        stackable: true,
        maxStack: 50,
        icon: '🏹',
        color: '#8B4513',
        value: 2,
        damage: 15
    },
    ARMOR: {
        id: 'armor',
        name: 'Armor',
        type: ITEM_TYPES.ARMOR,
        stackable: false,
        icon: '🛡️',
        color: '#C0C0C0',
        value: 40,
        defense: 15,
        durability: 100
    },
    WALL: {
        id: 'wall',
        name: 'Wall',
        type: ITEM_TYPES.BUILDING,
        stackable: true,
        maxStack: 99,
        icon: '🧱',
        color: '#696969',
        value: 5,
        health: 100
    },
    FOUNDATION: {
        id: 'foundation',
        name: 'Foundation',
        type: ITEM_TYPES.BUILDING,
        stackable: true,
        maxStack: 99,
        icon: '🏗️',
        color: '#8B4513',
        value: 8,
        health: 150
    },
    TURRET: {
        id: 'turret',
        name: 'Turret',
        type: ITEM_TYPES.BUILDING,
        stackable: false,
        icon: '🗼',
        color: '#C0C0C0',
        value: 50,
        health: 200,
        damage: 30,
        range: 300
    }
};

// Crafting Recipes
const CRAFTING_RECIPES = {
    AXE: {
        id: 'axe',
        name: 'Axe',
        result: ITEMS.AXE,
        ingredients: [
            { item: ITEMS.WOOD, amount: 3 },
            { item: ITEMS.STONE, amount: 2 }
        ],
        time: 2000
    },
    PICKAXE: {
        id: 'pickaxe',
        name: 'Pickaxe',
        result: ITEMS.PICKAXE,
        ingredients: [
            { item: ITEMS.WOOD, amount: 2 },
            { item: ITEMS.STONE, amount: 3 }
        ],
        time: 2000
    },
    SWORD: {
        id: 'sword',
        name: 'Sword',
        result: ITEMS.SWORD,
        ingredients: [
            { item: ITEMS.WOOD, amount: 2 },
            { item: ITEMS.STONE, amount: 4 }
        ],
        time: 3000
    },
    BOW: {
        id: 'bow',
        name: 'Bow',
        result: ITEMS.BOW,
        ingredients: [
            { item: ITEMS.WOOD, amount: 4 },
            { item: ITEMS.STONE, amount: 1 }
        ],
        time: 2500
    },
    ARROW: {
        id: 'arrow',
        name: 'Arrow',
        result: ITEMS.ARROW,
        ingredients: [
            { item: ITEMS.WOOD, amount: 1 },
            { item: ITEMS.STONE, amount: 1 }
        ],
        time: 1000,
        amount: 5
    },
    ARMOR: {
        id: 'armor',
        name: 'Armor',
        result: ITEMS.ARMOR,
        ingredients: [
            { item: ITEMS.WOOD, amount: 3 },
            { item: ITEMS.STONE, amount: 5 }
        ],
        time: 4000
    },
    WALL: {
        id: 'wall',
        name: 'Wall',
        result: ITEMS.WALL,
        ingredients: [
            { item: ITEMS.WOOD, amount: 2 },
            { item: ITEMS.STONE, amount: 3 }
        ],
        time: 1500
    },
    FOUNDATION: {
        id: 'foundation',
        name: 'Foundation',
        result: ITEMS.FOUNDATION,
        ingredients: [
            { item: ITEMS.WOOD, amount: 4 },
            { item: ITEMS.STONE, amount: 6 }
        ],
        time: 3000
    },
    TURRET: {
        id: 'turret',
        name: 'Turret',
        result: ITEMS.TURRET,
        ingredients: [
            { item: ITEMS.WOOD, amount: 8 },
            { item: ITEMS.STONE, amount: 12 },
            { item: ITEMS.SWORD, amount: 1 }
        ],
        time: 8000
    }
};

// Resource Types
const RESOURCE_TYPES = {
    TREE: 'tree',
    ROCK: 'rock',
    BERRY_BUSH: 'berry_bush',
    WATER_SOURCE: 'water_source'
};

// Resource Definitions
const RESOURCES = {
    TREE: {
        id: 'tree',
        name: 'Tree',
        type: RESOURCE_TYPES.TREE,
        health: 50,
        drops: [
            { item: ITEMS.WOOD, amount: 3, chance: 1.0 },
            { item: ITEMS.FOOD, amount: 1, chance: 0.3 }
        ],
        respawnTime: 30000,
        size: 40,
        color: '#228B22'
    },
    ROCK: {
        id: 'rock',
        name: 'Rock',
        type: RESOURCE_TYPES.ROCK,
        health: 80,
        drops: [
            { item: ITEMS.STONE, amount: 4, chance: 1.0 },
            { item: ITEMS.WOOD, amount: 1, chance: 0.2 }
        ],
        respawnTime: 45000,
        size: 35,
        color: '#696969'
    },
    BERRY_BUSH: {
        id: 'berry_bush',
        name: 'Berry Bush',
        type: RESOURCE_TYPES.BERRY_BUSH,
        health: 20,
        drops: [
            { item: ITEMS.FOOD, amount: 2, chance: 1.0 }
        ],
        respawnTime: 20000,
        size: 25,
        color: '#FF6B6B'
    },
    WATER_SOURCE: {
        id: 'water_source',
        name: 'Water Source',
        type: RESOURCE_TYPES.WATER_SOURCE,
        health: 1000,
        drops: [
            { item: ITEMS.WATER, amount: 1, chance: 1.0 }
        ],
        respawnTime: 10000,
        size: 30,
        color: '#4ECDC4',
        infinite: true
    }
};

// Building Types
const BUILDING_TYPES = {
    WALL: 'wall',
    FOUNDATION: 'foundation',
    TURRET: 'turret',
    CAMPFIRE: 'campfire',
    STORAGE: 'storage'
};

// Building Definitions
const BUILDINGS = {
    WALL: {
        id: 'wall',
        name: 'Wall',
        type: BUILDING_TYPES.WALL,
        health: 100,
        size: 32,
        color: '#696969',
        buildTime: 2000,
        cost: ITEMS.WALL
    },
    FOUNDATION: {
        id: 'foundation',
        name: 'Foundation',
        type: BUILDING_TYPES.FOUNDATION,
        health: 150,
        size: 64,
        color: '#8B4513',
        buildTime: 3000,
        cost: ITEMS.FOUNDATION
    },
    TURRET: {
        id: 'turret',
        name: 'Turret',
        type: BUILDING_TYPES.TURRET,
        health: 200,
        size: 40,
        color: '#C0C0C0',
        buildTime: 8000,
        cost: ITEMS.TURRET,
        damage: 30,
        range: 300,
        attackSpeed: 1000
    },
    CAMPFIRE: {
        id: 'campfire',
        name: 'Campfire',
        type: BUILDING_TYPES.CAMPFIRE,
        health: 50,
        size: 30,
        color: '#FF6B6B',
        buildTime: 1500,
        cost: { item: ITEMS.WOOD, amount: 5 },
        cookingSpeed: 1.5
    },
    STORAGE: {
        id: 'storage',
        name: 'Storage',
        type: BUILDING_TYPES.STORAGE,
        health: 120,
        size: 48,
        color: '#8B4513',
        buildTime: 4000,
        cost: { item: ITEMS.WOOD, amount: 8, item2: ITEMS.STONE, amount2: 4 },
        capacity: 50
    }
};

// NPC Types
const NPC_TYPES = {
    WOLF: 'wolf',
    BEAR: 'bear',
    DEER: 'deer',
    RABBIT: 'rabbit'
};

// NPC Definitions
const NPCS = {
    WOLF: {
        id: 'wolf',
        name: 'Wolf',
        type: NPC_TYPES.WOLF,
        health: 60,
        damage: 20,
        speed: 2.5,
        size: 28,
        color: '#8B4513',
        aggressive: true,
        drops: [
            { item: ITEMS.FOOD, amount: 2, chance: 0.8 },
            { item: ITEMS.WOOD, amount: 1, chance: 0.3 }
        ],
        attackRange: 40,
        chaseRange: 150
    },
    BEAR: {
        id: 'bear',
        name: 'Bear',
        type: NPC_TYPES.BEAR,
        health: 120,
        damage: 35,
        speed: 1.8,
        size: 40,
        color: '#654321',
        aggressive: true,
        drops: [
            { item: ITEMS.FOOD, amount: 4, chance: 1.0 },
            { item: ITEMS.WOOD, amount: 2, chance: 0.5 }
        ],
        attackRange: 50,
        chaseRange: 200
    },
    DEER: {
        id: 'deer',
        name: 'Deer',
        type: NPC_TYPES.DEER,
        health: 40,
        damage: 5,
        speed: 4.0,
        size: 32,
        color: '#DEB887',
        aggressive: false,
        drops: [
            { item: ITEMS.FOOD, amount: 3, chance: 1.0 }
        ],
        fleeRange: 120
    },
    RABBIT: {
        id: 'rabbit',
        name: 'Rabbit',
        type: NPC_TYPES.RABBIT,
        health: 20,
        damage: 2,
        speed: 3.5,
        size: 20,
        color: '#F5DEB3',
        aggressive: false,
        drops: [
            { item: ITEMS.FOOD, amount: 1, chance: 0.7 }
        ],
        fleeRange: 100
    }
};

// Experience Levels
const EXPERIENCE_LEVELS = [
    0, 100, 250, 450, 700, 1000, 1350, 1750, 2200, 2700, 3250, 3850, 4500, 5200, 5950, 6750, 7600, 8500, 9450, 10450
];

// Game States
const GAME_STATES = {
    LOADING: 'loading',
    HOME: 'home',
    PLAYING: 'playing',
    PAUSED: 'paused'
};

// UI States
const UI_STATES = {
    NONE: 'none',
    INVENTORY: 'inventory',
    BUILDING: 'building',
    SETTINGS: 'settings',
    CHAT: 'chat'
};

// Colors
const COLORS = {
    PRIMARY: '#00d4ff',
    SECONDARY: '#7b2cbf',
    SUCCESS: '#44ff44',
    WARNING: '#ffaa44',
    DANGER: '#ff4444',
    BACKGROUND: '#0a0a0a',
    SURFACE: '#1a1a2e',
    TEXT: '#ffffff',
    TEXT_SECONDARY: '#cccccc'
};

// Audio Settings
const AUDIO_SETTINGS = {
    MUSIC_VOLUME: 0.5,
    SFX_VOLUME: 0.7,
    MASTER_VOLUME: 1.0
};

// Input Keys
const KEYS = {
    W: 87,
    A: 65,
    S: 83,
    D: 68,
    SPACE: 32,
    SHIFT: 16,
    CTRL: 17,
    ESC: 27,
    I: 73,
    B: 66,
    ENTER: 13,
    TAB: 9
}; 
// Building Class
class Building extends Entity {
    constructor(x, y, buildingType, rotation = 0, config = {}) {
        const buildingData = BUILDINGS[buildingType];
        if (!buildingData) {
            throw new Error(`Unknown building type: ${buildingType}`);
        }

        super(x, y, 'building', {
            width: buildingData.size,
            height: buildingData.size,
            health: buildingData.health,
            maxHealth: buildingData.health,
            speed: 0,
            damage: 0,
            defense: 0,
            color: buildingData.color,
            collisionRadius: buildingData.size / 2,
            solid: true,
            renderLayer: 3,
            ...config
        });

        this.buildingType = buildingType;
        this.buildingData = buildingData;
        this.rotation = rotation;
        
        // Building state
        this.built = false;
        this.buildProgress = 0;
        this.buildTime = buildingData.buildTime || 2000;
        this.builder = null;
        
        // Building-specific properties
        this.owner = null;
        this.team = null;
        this.level = 1;
        this.maxLevel = 3;
        
        // Combat buildings
        this.attackCooldown = 0;
        this.attackRange = buildingData.range || 0;
        this.attackDamage = buildingData.damage || 0;
        this.attackSpeed = buildingData.attackSpeed || 1000;
        this.lastAttackTime = 0;
        this.target = null;
        
        // Storage buildings
        this.storageCapacity = buildingData.capacity || 0;
        this.storedItems = [];
        
        // Tags
        this.addTag('building');
        this.addTag('interactable');
        
        if (this.attackDamage > 0) {
            this.addTag('combat');
        }
        
        if (this.storageCapacity > 0) {
            this.addTag('storage');
        }
    }

    // Update building logic
    update(deltaTime) {
        super.update(deltaTime);
        
        this.updateBuilding(deltaTime);
        this.updateCombat(deltaTime);
        this.updateStorage(deltaTime);
    }

    // Update building construction
    updateBuilding(deltaTime) {
        if (!this.built && this.builder) {
            this.buildProgress += deltaTime;
            
            if (this.buildProgress >= this.buildTime) {
                this.completeBuild();
            }
        }
    }

    // Update combat for combat buildings
    updateCombat(deltaTime) {
        if (!this.built || this.attackDamage <= 0) return;
        
        this.attackCooldown = Math.max(0, this.attackCooldown - deltaTime);
        
        // Find targets
        if (!this.target || !this.target.isAlive()) {
            this.findTarget();
        }
        
        // Attack target
        if (this.target && this.attackCooldown <= 0) {
            const distance = this.getDistanceTo(this.target);
            if (distance <= this.attackRange) {
                this.attack(this.target);
            }
        }
    }

    // Update storage for storage buildings
    updateStorage(deltaTime) {
        // Storage buildings don't need active updates
    }

    // Start building
    startBuild(player) {
        if (this.built || this.builder) return false;
        
        this.builder = player;
        this.owner = player;
        this.buildProgress = 0;
        
        // Play build sound
        if (window.audioManager) {
            audioManager.playSound('build_start');
        }
        
        return true;
    }

    // Complete building
    completeBuild() {
        this.built = true;
        this.builder = null;
        this.buildProgress = 0;
        
        // Play build complete sound
        if (window.audioManager) {
            audioManager.playSound('build_complete');
        }
    }

    // Find target for combat buildings
    findTarget() {
        if (!window.game || !game.world) return;
        
        const nearbyEntities = game.world.getEntitiesInRange(this.x, this.y, this.attackRange);
        const enemies = nearbyEntities.filter(entity => 
            entity.hasTag('enemy') && entity.isAlive() && entity !== this
        );
        
        if (enemies.length > 0) {
            // Find closest enemy
            enemies.sort((a, b) => this.getDistanceTo(a) - this.getDistanceTo(b));
            this.target = enemies[0];
        }
    }

    // Attack target
    attack(target) {
        if (this.attackCooldown > 0) return false;
        
        // Create projectile
        if (window.game && game.world) {
            const projectile = new Projectile(
                this.x,
                this.y,
                target.x,
                target.y,
                this.attackDamage,
                this.owner || this
            );
            game.world.addEntity(projectile);
        }
        
        // Set attack cooldown
        this.attackCooldown = this.attackSpeed;
        this.lastAttackTime = Date.now();
        
        return true;
    }

    // Upgrade building
    upgrade() {
        if (this.level >= this.maxLevel) return false;
        
        this.level++;
        this.maxHealth *= 1.5;
        this.health = this.maxHealth;
        
        if (this.attackDamage > 0) {
            this.attackDamage *= 1.2;
            this.attackRange *= 1.1;
        }
        
        if (this.storageCapacity > 0) {
            this.storageCapacity *= 1.5;
        }
        
        return true;
    }

    // Add item to storage
    addToStorage(item, amount = 1) {
        if (!this.built || this.storageCapacity <= 0) return false;
        
        const currentStorage = this.storedItems.reduce((total, storedItem) => total + storedItem.amount, 0);
        if (currentStorage + amount > this.storageCapacity) return false;
        
        // Find existing item stack
        const existingItem = this.storedItems.find(storedItem => storedItem.id === item.id);
        if (existingItem) {
            existingItem.amount += amount;
        } else {
            this.storedItems.push({ ...item, amount });
        }
        
        return true;
    }

    // Remove item from storage
    removeFromStorage(itemId, amount = 1) {
        const itemIndex = this.storedItems.findIndex(item => item.id === itemId);
        if (itemIndex === -1) return false;
        
        const item = this.storedItems[itemIndex];
        if (item.amount < amount) return false;
        
        item.amount -= amount;
        if (item.amount <= 0) {
            this.storedItems.splice(itemIndex, 1);
        }
        
        return true;
    }

    // Get storage contents
    getStorageContents() {
        return [...this.storedItems];
    }

    // Get storage usage percentage
    getStorageUsage() {
        if (this.storageCapacity <= 0) return 0;
        const currentStorage = this.storedItems.reduce((total, item) => total + item.amount, 0);
        return currentStorage / this.storageCapacity;
    }

    // Check if building can be built
    canBuild() {
        return !this.built && !this.builder;
    }

    // Check if building is complete
    isComplete() {
        return this.built;
    }

    // Get build progress percentage
    getBuildProgress() {
        return this.buildProgress / this.buildTime;
    }

    // Override damage method
    takeDamage(amount, source = null) {
        const killed = super.takeDamage(amount, source);
        
        if (killed) {
            // Create destruction particles
            this.createDestructionParticles();
            
            // Play destruction sound
            if (window.audioManager) {
                audioManager.playSound('build_destroy');
            }
        }
        
        return killed;
    }

    // Create destruction particles
    createDestructionParticles() {
        for (let i = 0; i < 12; i++) {
            const particle = {
                x: this.x + Utils.random(-this.width / 2, this.width / 2),
                y: this.y + Utils.random(-this.height / 2, this.height / 2),
                vx: Utils.random(-3, 3),
                vy: Utils.random(-3, 3),
                life: 1.0,
                maxLife: 1.0,
                color: this.color,
                size: Utils.random(2, 6),
                update: function(deltaTime) {
                    this.x += this.vx;
                    this.y += this.vy;
                    this.vy += 0.1; // Gravity
                    this.life -= deltaTime;
                    
                    if (this.life <= 0) {
                        this.dead = true;
                    }
                },
                render: function(ctx) {
                    const alpha = this.life / this.maxLife;
                    ctx.save();
                    ctx.globalAlpha = alpha;
                    ctx.fillStyle = this.color;
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.restore();
                }
            };
            
            this.particles.push(particle);
        }
    }

    // Override render method
    renderEntity(ctx) {
        // Draw building foundation
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height);
        
        // Draw building icon
        ctx.fillStyle = '#ffffff';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(this.getBuildingIcon(), 0, 4);
        
        // Draw build progress if not complete
        if (!this.built) {
            const progress = this.getBuildProgress();
            const barWidth = this.width;
            const barHeight = 4;
            const barY = -this.height / 2 - 10;
            
            // Background
            ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.fillRect(-barWidth / 2, barY, barWidth, barHeight);
            
            // Progress fill
            ctx.fillStyle = '#00d4ff';
            ctx.fillRect(-barWidth / 2, barY, barWidth * progress, barHeight);
        }
        
        // Draw health bar if damaged
        if (this.health < this.maxHealth) {
            const barWidth = this.width;
            const barHeight = 3;
            const barY = -this.height / 2 - 15;
            
            // Background
            ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.fillRect(-barWidth / 2, barY, barWidth, barHeight);
            
            // Health fill
            const healthPercent = this.health / this.maxHealth;
            ctx.fillStyle = healthPercent > 0.5 ? '#44ff44' : healthPercent > 0.25 ? '#ffaa44' : '#ff4444';
            ctx.fillRect(-barWidth / 2, barY, barWidth * healthPercent, barHeight);
        }
        
        // Draw level indicator
        if (this.level > 1) {
            ctx.fillStyle = '#ffffff';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`Lv.${this.level}`, 0, -this.height / 2 - 5);
        }
        
        // Draw storage indicator
        if (this.storageCapacity > 0) {
            const usage = this.getStorageUsage();
            const indicatorWidth = this.width * 0.8;
            const indicatorHeight = 2;
            const indicatorY = this.height / 2 + 5;
            
            // Background
            ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.fillRect(-indicatorWidth / 2, indicatorY, indicatorWidth, indicatorHeight);
            
            // Usage fill
            ctx.fillStyle = usage > 0.8 ? '#ff4444' : usage > 0.5 ? '#ffaa44' : '#44ff44';
            ctx.fillRect(-indicatorWidth / 2, indicatorY, indicatorWidth * usage, indicatorHeight);
        }
    }

    // Get building icon
    getBuildingIcon() {
        switch (this.buildingType) {
            case 'wall': return '🧱';
            case 'foundation': return '🏗️';
            case 'turret': return '🗼';
            case 'campfire': return '🔥';
            case 'storage': return '📦';
            default: return '🏠';
        }
    }

    // Serialize building data
    serialize() {
        const data = super.serialize();
        return {
            ...data,
            buildingType: this.buildingType,
            built: this.built,
            buildProgress: this.buildProgress,
            buildTime: this.buildTime,
            builder: this.builder ? this.builder.id : null,
            owner: this.owner ? this.owner.id : null,
            team: this.team,
            level: this.level,
            attackCooldown: this.attackCooldown,
            target: this.target ? this.target.id : null,
            storageCapacity: this.storageCapacity,
            storedItems: this.storedItems
        };
    }

    // Deserialize building data
    deserialize(data) {
        super.deserialize(data);
        this.buildingType = data.buildingType;
        this.built = data.built || false;
        this.buildProgress = data.buildProgress || 0;
        this.buildTime = data.buildTime || 2000;
        this.owner = data.owner;
        this.team = data.team;
        this.level = data.level || 1;
        this.attackCooldown = data.attackCooldown || 0;
        this.target = data.target;
        this.storageCapacity = data.storageCapacity || 0;
        this.storedItems = data.storedItems || [];
    }
} 
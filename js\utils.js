// Utility Functions

// Math Utilities
const Utils = {
    // Random number between min and max
    random: (min, max) => {
        return Math.random() * (max - min) + min;
    },

    // Random integer between min and max (inclusive)
    randomInt: (min, max) => {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    },

    // Distance between two points
    distance: (x1, y1, x2, y2) => {
        return Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
    },

    // Angle between two points
    angle: (x1, y1, x2, y2) => {
        return Math.atan2(y2 - y1, x2 - x1);
    },

    // Clamp value between min and max
    clamp: (value, min, max) => {
        return Math.min(Math.max(value, min), max);
    },

    // Linear interpolation
    lerp: (start, end, factor) => {
        return start + (end - start) * factor;
    },

    // Smooth interpolation
    smoothStep: (edge0, edge1, x) => {
        const t = Utils.clamp((x - edge0) / (edge1 - edge0), 0.0, 1.0);
        return t * t * (3.0 - 2.0 * t);
    },

    // Convert degrees to radians
    toRadians: (degrees) => {
        return degrees * Math.PI / 180;
    },

    // Convert radians to degrees
    toDegrees: (radians) => {
        return radians * 180 / Math.PI;
    },

    // Check if point is inside rectangle
    pointInRect: (x, y, rectX, rectY, rectWidth, rectHeight) => {
        return x >= rectX && x <= rectX + rectWidth && 
               y >= rectY && y <= rectY + rectHeight;
    },

    // Check if two rectangles intersect
    rectIntersect: (rect1, rect2) => {
        return rect1.x < rect2.x + rect2.width &&
               rect1.x + rect1.width > rect2.x &&
               rect1.y < rect2.y + rect2.height &&
               rect1.y + rect1.height > rect2.y;
    },

    // Generate random position within world bounds
    randomWorldPosition: () => {
        const margin = 100;
        return {
            x: Utils.random(margin, GAME_CONFIG.WORLD_SIZE - margin),
            y: Utils.random(margin, GAME_CONFIG.WORLD_SIZE - margin)
        };
    },

    // Generate random position around a point
    randomPositionAround: (x, y, radius) => {
        const angle = Utils.random(0, Math.PI * 2);
        const distance = Utils.random(0, radius);
        return {
            x: x + Math.cos(angle) * distance,
            y: y + Math.sin(angle) * distance
        };
    },

    // Format time in seconds to MM:SS
    formatTime: (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    },

    // Format large numbers with K, M, B suffixes
    formatNumber: (num) => {
        if (num >= 1000000000) {
            return (num / 1000000000).toFixed(1) + 'B';
        } else if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    },

    // Deep clone an object
    deepClone: (obj) => {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => Utils.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = Utils.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },

    // Debounce function
    debounce: (func, wait) => {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Throttle function
    throttle: (func, limit) => {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // Generate unique ID
    generateId: () => {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },

    // Check if two objects are equal
    isEqual: (obj1, obj2) => {
        if (obj1 === obj2) return true;
        if (obj1 == null || obj2 == null) return false;
        if (obj1.constructor !== obj2.constructor) return false;

        if (obj1 instanceof Array) {
            if (obj1.length !== obj2.length) return false;
            for (let i = 0; i < obj1.length; i++) {
                if (!Utils.isEqual(obj1[i], obj2[i])) return false;
            }
            return true;
        }

        if (obj1 instanceof Object) {
            const keys1 = Object.keys(obj1);
            const keys2 = Object.keys(obj2);
            if (keys1.length !== keys2.length) return false;
            for (const key of keys1) {
                if (!obj2.hasOwnProperty(key)) return false;
                if (!Utils.isEqual(obj1[key], obj2[key])) return false;
            }
            return true;
        }

        return false;
    },

    // Color utilities
    hexToRgb: (hex) => {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    },

    rgbToHex: (r, g, b) => {
        return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    },

    // Interpolate between two colors
    interpolateColor: (color1, color2, factor) => {
        const rgb1 = Utils.hexToRgb(color1);
        const rgb2 = Utils.hexToRgb(color2);
        const r = Math.round(Utils.lerp(rgb1.r, rgb2.r, factor));
        const g = Math.round(Utils.lerp(rgb1.g, rgb2.g, factor));
        const b = Math.round(Utils.lerp(rgb1.b, rgb2.b, factor));
        return Utils.rgbToHex(r, g, b);
    },

    // Easing functions
    easeInOutQuad: (t) => {
        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
    },

    easeOutBounce: (t) => {
        if (t < 1 / 2.75) {
            return 7.5625 * t * t;
        } else if (t < 2 / 2.75) {
            return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
        } else if (t < 2.5 / 2.75) {
            return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
        } else {
            return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
        }
    },

    // Noise generation (simple)
    noise: (() => {
        const noise = {};
        let seed = Math.random();

        noise.setSeed = (s) => {
            seed = s;
        };

        noise.get = (x, y) => {
            const n = x + y * 57 + seed;
            return (Math.sin(n) * 43758.5453) % 1;
        };

        return noise;
    })(),

    // Perlin noise (simplified)
    perlinNoise: (() => {
        const permutation = [];
        const p = [];
        
        for (let i = 0; i < 256; i++) {
            permutation[i] = Math.floor(Math.random() * 256);
        }
        
        for (let i = 0; i < 512; i++) {
            p[i] = permutation[i & 255];
        }

        const fade = (t) => {
            return t * t * t * (t * (t * 6 - 15) + 10);
        };

        const lerp = (t, a, b) => {
            return a + t * (b - a);
        };

        const grad = (hash, x) => {
            return (hash & 1) === 0 ? x : -x;
        };

        return (x) => {
            const X = Math.floor(x) & 255;
            x -= Math.floor(x);
            const u = fade(x);
            
            return lerp(u, grad(p[X], x), grad(p[X + 1], x - 1)) * 2;
        };
    })(),

    // Array utilities
    shuffle: (array) => {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    },

    // Get random element from array
    randomElement: (array) => {
        return array[Math.floor(Math.random() * array.length)];
    },

    // Remove element from array
    removeFromArray: (array, element) => {
        const index = array.indexOf(element);
        if (index > -1) {
            array.splice(index, 1);
        }
        return array;
    },

    // Check if array contains element
    arrayContains: (array, element) => {
        return array.indexOf(element) !== -1;
    },

    // Get unique elements from array
    unique: (array) => {
        return [...new Set(array)];
    },

    // Sort array by property
    sortBy: (array, property, ascending = true) => {
        return array.sort((a, b) => {
            if (ascending) {
                return a[property] > b[property] ? 1 : -1;
            } else {
                return a[property] < b[property] ? 1 : -1;
            }
        });
    },

    // Group array by property
    groupBy: (array, property) => {
        return array.reduce((groups, item) => {
            const group = item[property];
            groups[group] = groups[group] || [];
            groups[group].push(item);
            return groups;
        }, {});
    },

    // String utilities
    capitalize: (str) => {
        return str.charAt(0).toUpperCase() + str.slice(1);
    },

    // Format string with placeholders
    formatString: (str, ...args) => {
        return str.replace(/{(\d+)}/g, (match, number) => {
            return typeof args[number] !== 'undefined' ? args[number] : match;
        });
    },

    // Validate email
    isValidEmail: (email) => {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },

    // Validate URL
    isValidUrl: (url) => {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    },

    // Local storage utilities
    storage: {
        set: (key, value) => {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (e) {
                console.error('Error saving to localStorage:', e);
                return false;
            }
        },

        get: (key, defaultValue = null) => {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (e) {
                console.error('Error reading from localStorage:', e);
                return defaultValue;
            }
        },

        remove: (key) => {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (e) {
                console.error('Error removing from localStorage:', e);
                return false;
            }
        },

        clear: () => {
            try {
                localStorage.clear();
                return true;
            } catch (e) {
                console.error('Error clearing localStorage:', e);
                return false;
            }
        }
    },

    // Performance utilities
    performance: {
        fps: 0,
        frameCount: 0,
        lastTime: 0,

        update: (currentTime) => {
            Utils.performance.frameCount++;
            if (currentTime - Utils.performance.lastTime >= 1000) {
                Utils.performance.fps = Utils.performance.frameCount;
                Utils.performance.frameCount = 0;
                Utils.performance.lastTime = currentTime;
            }
        },

        getFPS: () => {
            return Utils.performance.fps;
        }
    },

    // Debug utilities
    debug: {
        enabled: false,
        log: (...args) => {
            if (Utils.debug.enabled) {
                console.log('[DEBUG]', ...args);
            }
        },

        warn: (...args) => {
            if (Utils.debug.enabled) {
                console.warn('[DEBUG]', ...args);
            }
        },

        error: (...args) => {
            if (Utils.debug.enabled) {
                console.error('[DEBUG]', ...args);
            }
        },

        enable: () => {
            Utils.debug.enabled = true;
        },

        disable: () => {
            Utils.debug.enabled = false;
        }
    }
}; 
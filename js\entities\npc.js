// NPC Class
class NPC extends Entity {
    constructor(x, y, npcType, config = {}) {
        const npcData = NPCS[npcType];
        if (!npcData) {
            throw new Error(`Unknown NPC type: ${npcType}`);
        }

        super(x, y, 'npc', {
            width: npcData.size,
            height: npcData.size,
            health: npcData.health,
            maxHealth: npcData.health,
            speed: npcData.speed,
            damage: npcData.damage,
            defense: 0,
            color: npcData.color,
            collisionRadius: npcData.size / 2,
            solid: true,
            renderLayer: 5,
            ...config
        });

        this.npcType = npcType;
        this.npcData = npcData;
        
        // AI behavior
        this.aiState = 'idle'; // idle, wandering, chasing, attacking, fleeing
        this.targetEntity = null;
        this.lastTargetTime = 0;
        this.targetMemoryTime = 5000; // 5 seconds
        
        // Movement
        this.wanderRadius = 100;
        this.wanderCenter = { x: this.x, y: this.y };
        this.wanderTimer = 0;
        this.wanderInterval = Utils.random(2000, 5000);
        
        // Combat
        this.attackCooldown = 0;
        this.attackRange = npcData.attackRange || 40;
        this.chaseRange = npcData.chaseRange || 150;
        this.fleeRange = npcData.fleeRange || 100;
        
        // Aggression
        this.aggressive = npcData.aggressive || false;
        this.aggressionRange = 80;
        
        // Drops
        this.drops = npcData.drops || [];
        
        // Tags
        this.addTag('npc');
        this.addTag('ai');
        if (this.aggressive) {
            this.addTag('enemy');
        }
        
        // Set initial AI state
        this.setAIState('idle');
    }

    // Update NPC logic
    update(deltaTime) {
        super.update(deltaTime);
        
        this.updateAI(deltaTime);
        this.updateCombat(deltaTime);
        this.updateMovement(deltaTime);
    }

    // Update AI behavior
    updateAI(deltaTime) {
        // Find nearby players
        const nearbyPlayers = this.findNearbyPlayers();
        
        // Update AI state based on nearby players
        if (nearbyPlayers.length > 0) {
            const nearestPlayer = nearbyPlayers[0];
            const distance = this.getDistanceTo(nearestPlayer);
            
            if (this.aggressive) {
                if (distance <= this.attackRange) {
                    this.setAIState('attacking');
                    this.targetEntity = nearestPlayer;
                } else if (distance <= this.chaseRange) {
                    this.setAIState('chasing');
                    this.targetEntity = nearestPlayer;
                } else {
                    this.setAIState('idle');
                    this.targetEntity = null;
                }
            } else {
                // Non-aggressive NPCs flee from players
                if (distance <= this.fleeRange) {
                    this.setAIState('fleeing');
                    this.targetEntity = nearestPlayer;
                } else {
                    this.setAIState('idle');
                    this.targetEntity = null;
                }
            }
        } else {
            this.setAIState('idle');
            this.targetEntity = null;
        }
        
        // Update AI state behavior
        switch (this.aiState) {
            case 'idle':
                this.updateIdle(deltaTime);
                break;
            case 'wandering':
                this.updateWandering(deltaTime);
                break;
            case 'chasing':
                this.updateChasing(deltaTime);
                break;
            case 'attacking':
                this.updateAttacking(deltaTime);
                break;
            case 'fleeing':
                this.updateFleeing(deltaTime);
                break;
        }
    }

    // Update idle behavior
    updateIdle(deltaTime) {
        // Randomly start wandering
        this.wanderTimer += deltaTime;
        if (this.wanderTimer >= this.wanderInterval) {
            this.setAIState('wandering');
            this.wanderTimer = 0;
            this.wanderInterval = Utils.random(2000, 5000);
        }
    }

    // Update wandering behavior
    updateWandering(deltaTime) {
        // Move towards wander target
        const distance = Utils.distance(this.x, this.y, this.targetX, this.targetY);
        
        if (distance < 10) {
            // Reached target, find new one
            this.findNewWanderTarget();
        }
    }

    // Update chasing behavior
    updateChasing(deltaTime) {
        if (!this.targetEntity || !this.targetEntity.isAlive()) {
            this.setAIState('idle');
            return;
        }
        
        // Move towards target
        this.setTarget(this.targetEntity.x, this.targetEntity.y);
        
        // Check if target is too far
        const distance = this.getDistanceTo(this.targetEntity);
        if (distance > this.chaseRange) {
            this.setAIState('idle');
            this.targetEntity = null;
        }
    }

    // Update attacking behavior
    updateAttacking(deltaTime) {
        if (!this.targetEntity || !this.targetEntity.isAlive()) {
            this.setAIState('idle');
            return;
        }
        
        const distance = this.getDistanceTo(this.targetEntity);
        
        if (distance <= this.attackRange) {
            // Attack target
            this.attack(this.targetEntity);
        } else if (distance <= this.chaseRange) {
            // Chase target
            this.setAIState('chasing');
        } else {
            // Target too far
            this.setAIState('idle');
            this.targetEntity = null;
        }
    }

    // Update fleeing behavior
    updateFleeing(deltaTime) {
        if (!this.targetEntity) {
            this.setAIState('idle');
            return;
        }
        
        // Move away from target
        const angle = this.getAngleTo(this.targetEntity);
        const fleeDistance = 50;
        const fleeX = this.x - Math.cos(angle) * fleeDistance;
        const fleeY = this.y - Math.sin(angle) * fleeDistance;
        
        this.setTarget(fleeX, fleeY);
        
        // Check if far enough from target
        const distance = this.getDistanceTo(this.targetEntity);
        if (distance > this.fleeRange) {
            this.setAIState('idle');
            this.targetEntity = null;
        }
    }

    // Update combat
    updateCombat(deltaTime) {
        this.attackCooldown = Math.max(0, this.attackCooldown - deltaTime);
    }

    // Update movement
    updateMovement(deltaTime) {
        // Apply movement based on AI state
        if (this.aiState === 'chasing' || this.aiState === 'fleeing' || this.aiState === 'wandering') {
            super.updateMovement(deltaTime);
        }
    }

    // Find nearby players
    findNearbyPlayers() {
        if (!window.game || !game.world) return [];
        
        const nearbyEntities = game.world.getEntitiesInRange(this.x, this.y, this.chaseRange);
        return nearbyEntities.filter(entity => entity.hasTag('player') && entity.isAlive());
    }

    // Set AI state
    setAIState(state) {
        if (this.aiState === state) return;
        
        this.aiState = state;
        
        switch (state) {
            case 'idle':
                this.speed = this.npcData.speed;
                break;
            case 'wandering':
                this.findNewWanderTarget();
                this.speed = this.npcData.speed * 0.5;
                break;
            case 'chasing':
                this.speed = this.npcData.speed * 1.2;
                break;
            case 'attacking':
                this.speed = 0; // Stop to attack
                break;
            case 'fleeing':
                this.speed = this.npcData.speed * 1.5;
                break;
        }
    }

    // Find new wander target
    findNewWanderTarget() {
        const angle = Utils.random(0, Math.PI * 2);
        const distance = Utils.random(20, this.wanderRadius);
        
        this.targetX = this.wanderCenter.x + Math.cos(angle) * distance;
        this.targetY = this.wanderCenter.y + Math.sin(angle) * distance;
    }

    // Attack target
    attack(target) {
        if (this.attackCooldown > 0) return false;
        
        // Play attack sound
        if (window.audioManager) {
            audioManager.playCombatSound('npc_attack', true);
        }
        
        // Apply damage
        const killed = target.takeDamage(this.damage, this);
        
        // Set attack cooldown
        this.attackCooldown = 1000; // 1 second
        
        return killed;
    }

    // Override death method
    onDeath() {
        // Create drops
        this.createDrops();
        
        // Remove from world after delay
        setTimeout(() => {
            if (window.game && game.world) {
                game.world.removeEntity(this);
            }
        }, 2000);
    }

    // Create drops
    createDrops() {
        for (const drop of this.drops) {
            if (Math.random() < drop.chance) {
                const dropX = this.x + Utils.random(-10, 10);
                const dropY = this.y + Utils.random(-10, 10);
                
                if (window.game && game.world) {
                    const droppedItem = new DroppedItem(dropX, dropY, drop.item, drop.amount);
                    game.world.addEntity(droppedItem);
                }
            }
        }
    }

    // Override render method
    renderEntity(ctx) {
        // Draw NPC body
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.width / 2, 0, Math.PI * 2);
        ctx.fill();
        
        // Draw AI state indicator
        if (this.aiState !== 'idle') {
            ctx.fillStyle = this.getStateColor();
            ctx.beginPath();
            ctx.arc(0, 0, this.width / 2 + 2, 0, Math.PI * 2);
            ctx.stroke();
        }
        
        // Draw health bar
        if (this.health < this.maxHealth) {
            const barWidth = this.width;
            const barHeight = 3;
            const barY = -this.height / 2 - 8;
            
            // Background
            ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.fillRect(-barWidth / 2, barY, barWidth, barHeight);
            
            // Health fill
            const healthPercent = this.health / this.maxHealth;
            ctx.fillStyle = healthPercent > 0.5 ? '#44ff44' : healthPercent > 0.25 ? '#ffaa44' : '#ff4444';
            ctx.fillRect(-barWidth / 2, barY, barWidth * healthPercent, barHeight);
        }
    }

    // Get state color
    getStateColor() {
        switch (this.aiState) {
            case 'chasing': return '#ff4444';
            case 'attacking': return '#ff0000';
            case 'fleeing': return '#4444ff';
            case 'wandering': return '#44ff44';
            default: return '#ffffff';
        }
    }

    // Serialize NPC data
    serialize() {
        const data = super.serialize();
        return {
            ...data,
            npcType: this.npcType,
            aiState: this.aiState,
            targetEntity: this.targetEntity ? this.targetEntity.id : null,
            wanderCenter: this.wanderCenter,
            attackCooldown: this.attackCooldown
        };
    }

    // Deserialize NPC data
    deserialize(data) {
        super.deserialize(data);
        this.npcType = data.npcType;
        this.aiState = data.aiState || 'idle';
        this.wanderCenter = data.wanderCenter || { x: this.x, y: this.y };
        this.attackCooldown = data.attackCooldown || 0;
    }
}

// Dropped Item Class
class DroppedItem extends Entity {
    constructor(x, y, item, amount = 1) {
        super(x, y, 'dropped_item', {
            width: 16,
            height: 16,
            health: 1,
            maxHealth: 1,
            speed: 0,
            color: item.color || '#ffffff',
            collisionRadius: 8,
            solid: false,
            renderLayer: 1
        });

        this.item = item;
        this.amount = amount;
        this.pickupRange = 20;
        this.lifeTime = 30000; // 30 seconds
        this.spawnTime = Date.now();
        
        this.addTag('dropped_item');
        this.addTag('interactable');
    }

    // Update dropped item
    update(deltaTime) {
        super.update(deltaTime);
        
        // Check if item should despawn
        if (Date.now() - this.spawnTime > this.lifeTime) {
            this.despawn();
        }
    }

    // Try to pickup item
    pickup(player) {
        if (player.inventory.addItem(this.item, this.amount)) {
            // Play pickup sound
            if (window.audioManager) {
                audioManager.playSound('collect');
            }
            
            // Remove from world
            if (window.game && game.world) {
                game.world.removeEntity(this);
            }
            
            return true;
        }
        
        return false;
    }

    // Despawn item
    despawn() {
        if (window.game && game.world) {
            game.world.removeEntity(this);
        }
    }

    // Override render method
    renderEntity(ctx) {
        // Draw item icon
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height);
        
        // Draw item text
        ctx.fillStyle = '#ffffff';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(this.item.icon || '?', 0, 3);
        
        // Draw amount if more than 1
        if (this.amount > 1) {
            ctx.fillStyle = '#ffffff';
            ctx.font = '8px Arial';
            ctx.fillText(this.amount.toString(), this.width / 2 - 2, -this.height / 2 + 8);
        }
    }

    // Serialize dropped item
    serialize() {
        const data = super.serialize();
        return {
            ...data,
            item: this.item.id,
            amount: this.amount,
            spawnTime: this.spawnTime
        };
    }

    // Deserialize dropped item
    deserialize(data) {
        super.deserialize(data);
        this.item = ITEMS[data.item];
        this.amount = data.amount;
        this.spawnTime = data.spawnTime;
    }
} 
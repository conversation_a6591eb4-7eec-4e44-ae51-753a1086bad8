// Player Class
class Player extends Entity {
    constructor(x, y, config = {}) {
        super(x, y, 'player', {
            width: 32,
            height: 32,
            health: 100,
            maxHealth: 100,
            speed: GAME_CONFIG.PLAYER_SPEED,
            damage: 10,
            defense: 0,
            color: '#00d4ff',
            collisionRadius: 16,
            solid: true,
            renderLayer: 10,
            ...config
        });

        // Player stats
        this.level = 1;
        this.experience = 0;
        this.experienceToNext = 100;
        this.hunger = 100;
        this.maxHunger = 100;
        this.thirst = 100;
        this.maxThirst = 100;
        this.stamina = 100;
        this.maxStamina = 100;
        
        // Movement
        this.sprinting = false;
        this.sprintSpeed = this.speed * 1.5;
        this.walkingSpeed = this.speed;
        this.movementDirection = { x: 0, y: 0 };
        
        // Combat
        this.weapon = null;
        this.armor = null;
        this.attackCooldown = 0;
        this.attackRange = 40;
        this.lastAttackTime = 0;
        
        // Inventory
        this.inventory = new Inventory(32); // 32 slots
        this.selectedSlot = 0;
        this.hotbarSize = 8;
        
        // Building
        this.buildingMode = false;
        this.selectedBuilding = null;
        this.buildingPreview = null;
        this.buildingRotation = 0;
        
        // Crafting
        this.craftingQueue = [];
        this.craftingProgress = 0;
        this.craftingTime = 0;
        
        // Interaction
        this.interactionRange = 60;
        this.nearbyEntities = [];
        this.targetEntity = null;
        
        // Camera
        this.cameraTarget = { x: this.x, y: this.y };
        this.cameraSmoothness = 0.1;
        
        // Effects
        this.regenerationTimer = 0;
        this.hungerTimer = 0;
        this.thirstTimer = 0;
        
        // Tags
        this.addTag('player');
        this.addTag('controllable');
        
        // Initialize with basic items
        this.initializeStartingItems();
    }

    // Initialize starting items
    initializeStartingItems() {
        this.inventory.addItem(ITEMS.WOOD, 5);
        this.inventory.addItem(ITEMS.STONE, 3);
        this.inventory.addItem(ITEMS.FOOD, 2);
        this.inventory.addItem(ITEMS.WATER, 2);
    }

    // Update player logic
    update(deltaTime) {
        super.update(deltaTime);
        
        this.updateStats(deltaTime);
        this.updateMovement(deltaTime);
        this.updateCombat(deltaTime);
        this.updateCrafting(deltaTime);
        this.updateBuilding(deltaTime);
        this.updateInteraction(deltaTime);
        this.updateCamera(deltaTime);
        
        // Update nearby entities
        this.updateNearbyEntities();
    }

    // Update player stats
    updateStats(deltaTime) {
        // Regeneration
        this.regenerationTimer += deltaTime;
        if (this.regenerationTimer >= 1000) { // Every second
            if (this.hunger > 50 && this.thirst > 50) {
                this.heal(1);
            }
            this.regenerationTimer = 0;
        }
        
        // Hunger and thirst decay
        this.hungerTimer += deltaTime;
        this.thirstTimer += deltaTime;
        
        if (this.hungerTimer >= 30000) { // Every 30 seconds
            this.hunger = Math.max(0, this.hunger - 1);
            this.hungerTimer = 0;
        }
        
        if (this.thirstTimer >= 20000) { // Every 20 seconds
            this.thirst = Math.max(0, this.thirst - 1);
            this.thirstTimer = 0;
        }
        
        // Stamina regeneration
        if (!this.sprinting) {
            this.stamina = Math.min(this.maxStamina, this.stamina + deltaTime * 0.05);
        }
        
        // Death from hunger/thirst
        if (this.hunger <= 0 || this.thirst <= 0) {
            this.takeDamage(1);
        }
    }

    // Update movement
    updateMovement(deltaTime) {
        if (!inputManager) return;
        
        const direction = inputManager.getMovementDirection();
        this.movementDirection = direction;
        
        if (direction.x !== 0 || direction.y !== 0) {
            // Set sprinting
            this.sprinting = inputManager.isKeyPressed(KEYS.SHIFT) && this.stamina > 0;
            
            // Calculate speed
            let speed = this.walkingSpeed;
            if (this.sprinting) {
                speed = this.sprintSpeed;
                this.stamina = Math.max(0, this.stamina - deltaTime * 0.1);
            }
            
            // Move player
            const moveX = direction.x * speed * deltaTime;
            const moveY = direction.y * speed * deltaTime;
            
            // Check collision before moving
            const newX = this.x + moveX;
            const newY = this.y + moveY;
            
            if (this.canMoveTo(newX, newY)) {
                this.x = newX;
                this.y = newY;
                this.vx = moveX / deltaTime;
                this.vy = moveY / deltaTime;
                
                // Play footsteps
                if (window.audioManager) {
                    audioManager.playFootsteps(true);
                }
            }
        } else {
            this.vx = 0;
            this.vy = 0;
        }
    }

    // Update combat
    updateCombat(deltaTime) {
        this.attackCooldown = Math.max(0, this.attackCooldown - deltaTime);
        
        // Auto-attack nearby enemies
        if (this.targetEntity && this.targetEntity.isAlive()) {
            const distance = this.getDistanceTo(this.targetEntity);
            if (distance <= this.attackRange && this.attackCooldown <= 0) {
                this.attack(this.targetEntity);
            }
        }
    }

    // Update crafting
    updateCrafting(deltaTime) {
        if (this.craftingQueue.length > 0) {
            const currentCraft = this.craftingQueue[0];
            this.craftingTime += deltaTime;
            
            if (this.craftingTime >= currentCraft.time) {
                this.completeCrafting(currentCraft);
                this.craftingQueue.shift();
                this.craftingTime = 0;
            }
        }
    }

    // Update building
    updateBuilding(deltaTime) {
        if (this.buildingMode && this.selectedBuilding && inputManager) {
            const mousePos = inputManager.getMouseWorldPosition();
            this.buildingPreview = {
                x: Math.round(mousePos.x / GAME_CONFIG.TILE_SIZE) * GAME_CONFIG.TILE_SIZE,
                y: Math.round(mousePos.y / GAME_CONFIG.TILE_SIZE) * GAME_CONFIG.TILE_SIZE,
                type: this.selectedBuilding,
                rotation: this.buildingRotation
            };
        }
    }

    // Update interaction
    updateInteraction(deltaTime) {
        // Find nearby interactable entities
        this.nearbyEntities = [];
        if (window.game && game.world) {
            game.world.getEntitiesInRange(this.x, this.y, this.interactionRange).forEach(entity => {
                if (entity !== this && entity.hasTag('interactable')) {
                    this.nearbyEntities.push(entity);
                }
            });
        }
        
        // Auto-target nearest entity
        if (this.nearbyEntities.length > 0) {
            this.nearbyEntities.sort((a, b) => this.getDistanceTo(a) - this.getDistanceTo(b));
            this.targetEntity = this.nearbyEntities[0];
        } else {
            this.targetEntity = null;
        }
    }

    // Update camera
    updateCamera(deltaTime) {
        // Smooth camera following
        this.cameraTarget.x = Utils.lerp(this.cameraTarget.x, this.x, this.cameraSmoothness);
        this.cameraTarget.y = Utils.lerp(this.cameraTarget.y, this.y, this.cameraSmoothness);
        
        if (window.game && game.camera) {
            game.camera.follow(this.cameraTarget.x, this.cameraTarget.y);
        }
    }

    // Update nearby entities
    updateNearbyEntities() {
        // This is called from the main update loop
        // Nearby entities are updated in updateInteraction
    }

    // Check if player can move to position
    canMoveTo(x, y) {
        if (!window.game || !game.world) return true;
        
        // Check collision with solid entities
        const nearbyEntities = game.world.getEntitiesInRange(x, y, this.collisionRadius);
        for (const entity of nearbyEntities) {
            if (entity.solid && entity !== this) {
                const distance = Utils.distance(x, y, entity.x, entity.y);
                if (distance < this.collisionRadius + entity.collisionRadius) {
                    return false;
                }
            }
        }
        
        return true;
    }

    // Attack target
    attack(target) {
        if (this.attackCooldown > 0 || !target.isAlive()) return false;
        
        const weapon = this.getEquippedWeapon();
        const damage = weapon ? weapon.damage : this.damage;
        
        // Play attack sound
        if (window.audioManager) {
            audioManager.playCombatSound(weapon ? weapon.type : 'fist', true);
        }
        
        // Apply damage
        const killed = target.takeDamage(damage, this);
        
        // Set attack cooldown
        this.attackCooldown = weapon ? 1000 : 500; // 1 second for weapons, 0.5 for fists
        
        // Gain experience
        if (killed) {
            this.gainExperience(10);
        }
        
        return killed;
    }

    // Get equipped weapon
    getEquippedWeapon() {
        return this.weapon;
    }

    // Equip weapon
    equipWeapon(weapon) {
        this.weapon = weapon;
        this.attackRange = weapon ? weapon.range || 40 : 40;
    }

    // Unequip weapon
    unequipWeapon() {
        this.weapon = null;
        this.attackRange = 40;
    }

    // Use item
    useItem(slotIndex) {
        const item = this.inventory.getItem(slotIndex);
        if (!item) return false;
        
        switch (item.type) {
            case ITEM_TYPES.FOOD:
                return this.consumeFood(item);
            case ITEM_TYPES.WEAPON:
                return this.equipWeapon(item);
            case ITEM_TYPES.ARMOR:
                return this.equipArmor(item);
            case ITEM_TYPES.TOOL:
                return this.equipTool(item);
            default:
                return false;
        }
    }

    // Consume food
    consumeFood(food) {
        if (food.nutrition) {
            this.hunger = Math.min(this.maxHunger, this.hunger + food.nutrition);
        }
        if (food.hydration) {
            this.thirst = Math.min(this.maxThirst, this.thirst + food.hydration);
        }
        
        // Play eat sound
        if (window.audioManager) {
            audioManager.playSound('player_eat');
        }
        
        return true;
    }

    // Equip armor
    equipArmor(armor) {
        this.armor = armor;
        this.defense = armor.defense || 0;
        return true;
    }

    // Equip tool
    equipTool(tool) {
        this.weapon = tool;
        this.attackRange = tool.range || 40;
        return true;
    }

    // Start crafting
    startCrafting(recipeId) {
        const recipe = CRAFTING_RECIPES[recipeId];
        if (!recipe) return false;
        
        // Check if we have materials
        if (!this.hasCraftingMaterials(recipe)) return false;
        
        // Consume materials
        this.consumeCraftingMaterials(recipe);
        
        // Add to crafting queue
        this.craftingQueue.push(recipe);
        
        return true;
    }

    // Check if player has crafting materials
    hasCraftingMaterials(recipe) {
        for (const ingredient of recipe.ingredients) {
            const count = this.inventory.getItemCount(ingredient.item);
            if (count < ingredient.amount) {
                return false;
            }
        }
        return true;
    }

    // Consume crafting materials
    consumeCraftingMaterials(recipe) {
        for (const ingredient of recipe.ingredients) {
            this.inventory.removeItem(ingredient.item, ingredient.amount);
        }
    }

    // Complete crafting
    completeCrafting(recipe) {
        const amount = recipe.amount || 1;
        this.inventory.addItem(recipe.result, amount);
        
        // Play crafting sound
        if (window.audioManager) {
            audioManager.playSound('build_complete');
        }
    }

    // Gain experience
    gainExperience(amount) {
        this.experience += amount;
        
        // Check for level up
        while (this.experience >= this.experienceToNext) {
            this.levelUp();
        }
    }

    // Level up
    levelUp() {
        this.level++;
        this.experience -= this.experienceToNext;
        this.experienceToNext = EXPERIENCE_LEVELS[this.level] || this.experienceToNext * 1.2;
        
        // Increase stats
        this.maxHealth += 10;
        this.health = this.maxHealth;
        this.maxHunger += 5;
        this.maxThirst += 5;
        this.maxStamina += 5;
        
        // Play level up sound
        if (window.audioManager) {
            audioManager.playSound('player_levelup');
        }
    }

    // Set sprinting
    setSprinting(sprinting) {
        this.sprinting = sprinting;
    }

    // Toggle building mode
    toggleBuildingMode() {
        this.buildingMode = !this.buildingMode;
        if (!this.buildingMode) {
            this.buildingPreview = null;
            this.selectedBuilding = null;
        }
    }

    // Select building
    selectBuilding(buildingType) {
        this.selectedBuilding = buildingType;
        this.buildingMode = true;
    }

    // Place building
    placeBuilding() {
        if (!this.buildingPreview || !this.selectedBuilding) return false;
        
        const building = BUILDINGS[this.selectedBuilding];
        if (!building) return false;
        
        // Check if we have the required materials
        if (!this.hasBuildingMaterials(building)) return false;
        
        // Consume materials
        this.consumeBuildingMaterials(building);
        
        // Create building entity
        if (window.game && game.world) {
            const buildingEntity = new Building(
                this.buildingPreview.x,
                this.buildingPreview.y,
                this.selectedBuilding,
                this.buildingPreview.rotation
            );
            game.world.addEntity(buildingEntity);
        }
        
        // Play building sound
        if (window.audioManager) {
            audioManager.playSound('build_complete');
        }
        
        return true;
    }

    // Check if player has building materials
    hasBuildingMaterials(building) {
        if (building.cost && building.cost.item) {
            const count = this.inventory.getItemCount(building.cost.item);
            return count >= building.cost.amount;
        }
        return true;
    }

    // Consume building materials
    consumeBuildingMaterials(building) {
        if (building.cost && building.cost.item) {
            this.inventory.removeItem(building.cost.item, building.cost.amount);
        }
    }

    // Interact with entity
    interact(entity) {
        if (!entity || !entity.hasTag('interactable')) return false;
        
        // Handle different interaction types
        if (entity.hasTag('resource')) {
            return this.harvestResource(entity);
        } else if (entity.hasTag('npc')) {
            return this.interactWithNPC(entity);
        } else if (entity.hasTag('building')) {
            return this.interactWithBuilding(entity);
        }
        
        return false;
    }

    // Harvest resource
    harvestResource(resource) {
        const distance = this.getDistanceTo(resource);
        if (distance > this.interactionRange) return false;
        
        // Get appropriate tool
        const tool = this.getBestTool(resource.type);
        
        // Calculate harvest time
        const harvestTime = tool ? 1000 : 2000; // 1 second with tool, 2 without
        
        // Start harvesting
        if (!this.isHarvesting) {
            this.isHarvesting = true;
            this.harvestTarget = resource;
            this.harvestProgress = 0;
            this.harvestTime = harvestTime;
        }
        
        return true;
    }

    // Get best tool for resource
    getBestTool(resourceType) {
        // Check inventory for appropriate tools
        const tools = this.inventory.getItemsByType(ITEM_TYPES.TOOL);
        for (const tool of tools) {
            if (tool.suitableFor && tool.suitableFor.includes(resourceType)) {
                return tool;
            }
        }
        return null;
    }

    // Interact with NPC
    interactWithNPC(npc) {
        // Handle NPC interaction
        return true;
    }

    // Interact with building
    interactWithBuilding(building) {
        // Handle building interaction
        return true;
    }

    // Override render method
    renderEntity(ctx) {
        // Draw player body
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.width / 2, 0, Math.PI * 2);
        ctx.fill();
        
        // Draw weapon if equipped
        if (this.weapon) {
            ctx.save();
            ctx.rotate(this.rotation);
            ctx.fillStyle = this.weapon.color || '#ffffff';
            ctx.fillRect(0, -2, 20, 4);
            ctx.restore();
        }
        
        // Draw health bar
        if (this.health < this.maxHealth) {
            const barWidth = this.width;
            const barHeight = 4;
            const barY = -this.height / 2 - 10;
            
            // Background
            ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.fillRect(-barWidth / 2, barY, barWidth, barHeight);
            
            // Health fill
            const healthPercent = this.health / this.maxHealth;
            ctx.fillStyle = healthPercent > 0.5 ? '#44ff44' : healthPercent > 0.25 ? '#ffaa44' : '#ff4444';
            ctx.fillRect(-barWidth / 2, barY, barWidth * healthPercent, barHeight);
        }
        
        // Draw name if it exists
        if (this.name) {
            ctx.fillStyle = '#ffffff';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(this.name, 0, -this.height / 2 - 20);
        }
    }

    // Override death method
    onDeath() {
        // Drop inventory
        this.dropInventory();
        
        // Respawn after delay
        setTimeout(() => {
            this.respawn();
        }, 3000);
    }

    // Drop inventory
    dropInventory() {
        // Create item drops around player
        this.inventory.items.forEach((item, index) => {
            if (item) {
                // Create dropped item entity
                const dropX = this.x + Utils.random(-20, 20);
                const dropY = this.y + Utils.random(-20, 20);
                
                if (window.game && game.world) {
                    const droppedItem = new DroppedItem(dropX, dropY, item);
                    game.world.addEntity(droppedItem);
                }
            }
        });
        
        // Clear inventory
        this.inventory.clear();
    }

    // Respawn player
    respawn() {
        // Find safe spawn location
        const spawnPos = this.findSafeSpawnLocation();
        
        // Reset player
        this.x = spawnPos.x;
        this.y = spawnPos.y;
        this.health = this.maxHealth;
        this.hunger = this.maxHunger;
        this.thirst = this.maxThirst;
        this.stamina = this.maxStamina;
        this.dead = false;
        this.active = true;
        
        // Give basic items
        this.initializeStartingItems();
    }

    // Find safe spawn location
    findSafeSpawnLocation() {
        // Simple spawn logic - find empty area
        for (let attempts = 0; attempts < 10; attempts++) {
            const pos = Utils.randomWorldPosition();
            if (this.canMoveTo(pos.x, pos.y)) {
                return pos;
            }
        }
        
        // Fallback to center
        return { x: GAME_CONFIG.WORLD_SIZE / 2, y: GAME_CONFIG.WORLD_SIZE / 2 };
    }

    // Serialize player data
    serialize() {
        const data = super.serialize();
        return {
            ...data,
            level: this.level,
            experience: this.experience,
            experienceToNext: this.experienceToNext,
            hunger: this.hunger,
            maxHunger: this.maxHunger,
            thirst: this.thirst,
            maxThirst: this.maxThirst,
            stamina: this.stamina,
            maxStamina: this.maxStamina,
            inventory: this.inventory.serialize(),
            weapon: this.weapon ? this.weapon.id : null,
            armor: this.armor ? this.armor.id : null
        };
    }

    // Deserialize player data
    deserialize(data) {
        super.deserialize(data);
        this.level = data.level || 1;
        this.experience = data.experience || 0;
        this.experienceToNext = data.experienceToNext || 100;
        this.hunger = data.hunger || 100;
        this.maxHunger = data.maxHunger || 100;
        this.thirst = data.thirst || 100;
        this.maxThirst = data.maxThirst || 100;
        this.stamina = data.stamina || 100;
        this.maxStamina = data.maxStamina || 100;
        
        if (data.inventory) {
            this.inventory.deserialize(data.inventory);
        }
        
        if (data.weapon) {
            this.weapon = ITEMS[data.weapon];
        }
        
        if (data.armor) {
            this.armor = ITEMS[data.armor];
        }
    }
}

// Inventory Class
class Inventory {
    constructor(size = 32) {
        this.size = size;
        this.items = new Array(size).fill(null);
    }

    // Add item to inventory
    addItem(item, amount = 1) {
        if (typeof item === 'string') {
            item = ITEMS[item];
        }
        
        if (!item) return false;
        
        // Find existing stack
        if (item.stackable) {
            for (let i = 0; i < this.size; i++) {
                if (this.items[i] && this.items[i].id === item.id) {
                    const space = item.maxStack - this.items[i].amount;
                    const toAdd = Math.min(amount, space);
                    this.items[i].amount += toAdd;
                    amount -= toAdd;
                    
                    if (amount <= 0) return true;
                }
            }
        }
        
        // Find empty slot
        while (amount > 0) {
            const emptySlot = this.items.findIndex(item => item === null);
            if (emptySlot === -1) return false; // Inventory full
            
            const toAdd = Math.min(amount, item.maxStack || 1);
            this.items[emptySlot] = {
                ...item,
                amount: toAdd
            };
            amount -= toAdd;
        }
        
        return true;
    }

    // Remove item from inventory
    removeItem(itemId, amount = 1) {
        let remaining = amount;
        
        for (let i = this.size - 1; i >= 0; i--) {
            if (this.items[i] && this.items[i].id === itemId) {
                const toRemove = Math.min(remaining, this.items[i].amount);
                this.items[i].amount -= toRemove;
                remaining -= toRemove;
                
                if (this.items[i].amount <= 0) {
                    this.items[i] = null;
                }
                
                if (remaining <= 0) return true;
            }
        }
        
        return false;
    }

    // Get item at slot
    getItem(slot) {
        return this.items[slot] || null;
    }

    // Get item count
    getItemCount(itemId) {
        let count = 0;
        for (const item of this.items) {
            if (item && item.id === itemId) {
                count += item.amount;
            }
        }
        return count;
    }

    // Get items by type
    getItemsByType(type) {
        const items = [];
        for (const item of this.items) {
            if (item && item.type === type) {
                items.push(item);
            }
        }
        return items;
    }

    // Clear inventory
    clear() {
        this.items.fill(null);
    }

    // Serialize inventory
    serialize() {
        return {
            size: this.size,
            items: this.items.map(item => item ? { ...item } : null)
        };
    }

    // Deserialize inventory
    deserialize(data) {
        this.size = data.size;
        this.items = data.items.map(item => item ? { ...item } : null);
    }
} 
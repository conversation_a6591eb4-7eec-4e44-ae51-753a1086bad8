// Resource Class
class Resource extends Entity {
    constructor(x, y, resourceType, config = {}) {
        const resourceData = RESOURCES[resourceType];
        if (!resourceData) {
            throw new Error(`Unknown resource type: ${resourceType}`);
        }

        super(x, y, 'resource', {
            width: resourceData.size,
            height: resourceData.size,
            health: resourceData.health,
            maxHealth: resourceData.health,
            speed: 0,
            damage: 0,
            defense: 0,
            color: resourceData.color,
            collisionRadius: resourceData.size / 2,
            solid: true,
            renderLayer: 2,
            ...config
        });

        this.resourceType = resourceType;
        this.resourceData = resourceData;
        
        // Harvesting
        this.beingHarvested = false;
        this.harvester = null;
        this.harvestProgress = 0;
        this.harvestTime = 2000; // 2 seconds base
        this.lastHarvestTime = 0;
        
        // Respawn
        this.respawnTime = resourceData.respawnTime || 30000;
        this.respawnTimer = 0;
        this.originalHealth = resourceData.health;
        this.infinite = resourceData.infinite || false;
        
        // Drops
        this.drops = resourceData.drops || [];
        
        // Tags
        this.addTag('resource');
        this.addTag('interactable');
        this.addTag('harvestable');
    }

    // Update resource logic
    update(deltaTime) {
        super.update(deltaTime);
        
        this.updateHarvesting(deltaTime);
        this.updateRespawn(deltaTime);
    }

    // Update harvesting
    updateHarvesting(deltaTime) {
        if (this.beingHarvested && this.harvester) {
            this.harvestProgress += deltaTime;
            
            // Check if harvesting is complete
            if (this.harvestProgress >= this.harvestTime) {
                this.completeHarvest();
            }
        }
    }

    // Update respawn
    updateRespawn(deltaTime) {
        if (this.dead && !this.infinite) {
            this.respawnTimer += deltaTime;
            
            if (this.respawnTimer >= this.respawnTime) {
                this.respawn();
            }
        }
    }

    // Start harvesting
    startHarvest(player) {
        if (this.dead || this.beingHarvested) return false;
        
        this.beingHarvested = true;
        this.harvester = player;
        this.harvestProgress = 0;
        
        // Calculate harvest time based on player's tool
        const tool = player.getEquippedWeapon();
        if (tool && tool.type === ITEM_TYPES.TOOL) {
            this.harvestTime = 1000; // 1 second with tool
        } else {
            this.harvestTime = 2000; // 2 seconds without tool
        }
        
        // Play harvest sound
        if (window.audioManager) {
            audioManager.playResourceSound(this.resourceType);
        }
        
        return true;
    }

    // Stop harvesting
    stopHarvest() {
        this.beingHarvested = false;
        this.harvester = null;
        this.harvestProgress = 0;
    }

    // Complete harvest
    completeHarvest() {
        // Create drops
        this.createDrops();
        
        // Take damage
        this.takeDamage(this.health);
        
        // Stop harvesting
        this.stopHarvest();
        
        // Play harvest complete sound
        if (window.audioManager) {
            audioManager.playSound('collect');
        }
    }

    // Create drops
    createDrops() {
        for (const drop of this.drops) {
            if (Math.random() < drop.chance) {
                const dropX = this.x + Utils.random(-10, 10);
                const dropY = this.y + Utils.random(-10, 10);
                
                if (window.game && game.world) {
                    const droppedItem = new DroppedItem(dropX, dropY, drop.item, drop.amount);
                    game.world.addEntity(droppedItem);
                }
            }
        }
    }

    // Respawn resource
    respawn() {
        this.dead = false;
        this.active = true;
        this.health = this.originalHealth;
        this.respawnTimer = 0;
        this.beingHarvested = false;
        this.harvester = null;
        this.harvestProgress = 0;
    }

    // Get harvest progress percentage
    getHarvestProgress() {
        return this.harvestProgress / this.harvestTime;
    }

    // Check if resource can be harvested
    canHarvest() {
        return !this.dead && !this.beingHarvested;
    }

    // Override death method
    onDeath() {
        // Don't create death particles for resources
        this.active = false;
        
        // Start respawn timer if not infinite
        if (!this.infinite) {
            this.respawnTimer = 0;
        }
    }

    // Override render method
    renderEntity(ctx) {
        // Draw resource body
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.width / 2, 0, Math.PI * 2);
        ctx.fill();
        
        // Draw resource icon
        ctx.fillStyle = '#ffffff';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(this.getResourceIcon(), 0, 3);
        
        // Draw health bar if damaged
        if (this.health < this.maxHealth) {
            const barWidth = this.width;
            const barHeight = 3;
            const barY = -this.height / 2 - 8;
            
            // Background
            ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.fillRect(-barWidth / 2, barY, barWidth, barHeight);
            
            // Health fill
            const healthPercent = this.health / this.maxHealth;
            ctx.fillStyle = healthPercent > 0.5 ? '#44ff44' : healthPercent > 0.25 ? '#ffaa44' : '#ff4444';
            ctx.fillRect(-barWidth / 2, barY, barWidth * healthPercent, barHeight);
        }
        
        // Draw harvest progress if being harvested
        if (this.beingHarvested) {
            const progress = this.getHarvestProgress();
            const barWidth = this.width;
            const barHeight = 4;
            const barY = -this.height / 2 - 15;
            
            // Background
            ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.fillRect(-barWidth / 2, barY, barWidth, barHeight);
            
            // Progress fill
            ctx.fillStyle = '#00d4ff';
            ctx.fillRect(-barWidth / 2, barY, barWidth * progress, barHeight);
        }
    }

    // Get resource icon
    getResourceIcon() {
        switch (this.resourceType) {
            case 'tree': return '🌳';
            case 'rock': return '🪨';
            case 'berry_bush': return '🫐';
            case 'water_source': return '💧';
            default: return '📦';
        }
    }

    // Serialize resource data
    serialize() {
        const data = super.serialize();
        return {
            ...data,
            resourceType: this.resourceType,
            beingHarvested: this.beingHarvested,
            harvester: this.harvester ? this.harvester.id : null,
            harvestProgress: this.harvestProgress,
            harvestTime: this.harvestTime,
            respawnTimer: this.respawnTimer,
            originalHealth: this.originalHealth,
            infinite: this.infinite
        };
    }

    // Deserialize resource data
    deserialize(data) {
        super.deserialize(data);
        this.resourceType = data.resourceType;
        this.beingHarvested = data.beingHarvested || false;
        this.harvestProgress = data.harvestProgress || 0;
        this.harvestTime = data.harvestTime || 2000;
        this.respawnTimer = data.respawnTimer || 0;
        this.originalHealth = data.originalHealth || this.maxHealth;
        this.infinite = data.infinite || false;
    }
}

// Resource Spawner Class
class ResourceSpawner {
    constructor(world) {
        this.world = world;
        this.spawnTimer = 0;
        this.spawnInterval = 10000; // 10 seconds
        this.maxResources = GAME_CONFIG.MAX_RESOURCES;
        this.spawnChance = GAME_CONFIG.RESOURCE_SPAWN_RATE;
    }

    // Update spawner
    update(deltaTime) {
        this.spawnTimer += deltaTime;
        
        if (this.spawnTimer >= this.spawnInterval) {
            this.trySpawnResource();
            this.spawnTimer = 0;
        }
    }

    // Try to spawn a resource
    trySpawnResource() {
        if (Math.random() > this.spawnChance) return;
        
        // Count current resources
        const currentResources = this.world.getEntitiesByType('resource').length;
        if (currentResources >= this.maxResources) return;
        
        // Find spawn position
        const spawnPos = this.findSpawnPosition();
        if (!spawnPos) return;
        
        // Choose random resource type
        const resourceTypes = Object.keys(RESOURCES);
        const resourceType = resourceTypes[Math.floor(Math.random() * resourceTypes.length)];
        
        // Create resource
        const resource = new Resource(spawnPos.x, spawnPos.y, resourceType);
        this.world.addEntity(resource);
    }

    // Find valid spawn position
    findSpawnPosition() {
        for (let attempts = 0; attempts < 10; attempts++) {
            const pos = Utils.randomWorldPosition();
            
            // Check if position is clear
            const nearbyEntities = this.world.getEntitiesInRange(pos.x, pos.y, 50);
            if (nearbyEntities.length === 0) {
                return pos;
            }
        }
        
        return null;
    }
} 
// Main Game Initialization
class GameManager {
    constructor() {
        this.game = null;
        this.currentState = GAME_STATES.LOADING;
        this.loadingProgress = 0;
        this.loadingSteps = [
            'Initializing game...',
            'Loading audio system...',
            'Setting up input...',
            'Creating world...',
            'Spawning entities...',
            'Starting game...'
        ];
        this.currentStep = 0;
        
        this.init();
    }

    // Initialize the game
    init() {
        console.log('Survival.io - Game Initialization');
        
        // Show loading screen
        this.showLoadingScreen();
        
        // Start loading process
        this.loadGame();
    }

    // Load the game step by step
    async loadGame() {
        try {
            // Step 1: Initialize audio
            await this.loadAudio();
            
            // Step 2: Initialize input
            await this.loadInput();
            
            // Step 3: Initialize world
            await this.loadWorld();
            
            // Step 4: Initialize UI
            await this.loadUI();
            
            // Step 5: Start game
            await this.startGame();
            
        } catch (error) {
            console.error('Game loading failed:', error);
            this.showError('Failed to load game. Please refresh the page.');
        }
    }

    // Load audio system
    async loadAudio() {
        this.updateLoadingProgress(1, 'Loading audio system...');
        
        // Initialize audio manager
        audioManager = new AudioManager();
        audioManager.loadSettings();
        
        // Play menu music
        audioManager.playMusic('menu');
        
        await this.delay(500);
    }

    // Load input system
    async loadInput() {
        this.updateLoadingProgress(2, 'Setting up input...');
        
        // Initialize input manager
        inputManager = new InputManager();
        
        await this.delay(300);
    }

    // Load world system
    async loadWorld() {
        this.updateLoadingProgress(3, 'Creating world...');
        
        // Initialize game world
        this.game = new Game();
        
        await this.delay(400);
    }

    // Load UI system
    async loadUI() {
        this.updateLoadingProgress(4, 'Initializing UI...');
        
        // Initialize UI components
        this.game.initUI();
        
        await this.delay(300);
    }

    // Start the game
    async startGame() {
        this.updateLoadingProgress(5, 'Starting game...');
        
        // Start game loop
        this.game.start();
        
        // Switch to home screen
        this.showHomeScreen();
        
        await this.delay(200);
        
        // Hide loading screen
        this.hideLoadingScreen();
    }

    // Update loading progress
    updateLoadingProgress(step, message) {
        this.currentStep = step;
        this.loadingProgress = (step / this.loadingSteps.length) * 100;
        
        // Update loading bar
        const loadingFill = document.getElementById('loadingFill');
        const loadingText = document.getElementById('loadingText');
        
        if (loadingFill) {
            loadingFill.style.width = this.loadingProgress + '%';
        }
        
        if (loadingText) {
            loadingText.textContent = message;
        }
    }

    // Show loading screen
    showLoadingScreen() {
        this.hideAllScreens();
        document.getElementById('loadingScreen').classList.add('active');
    }

    // Show home screen
    showHomeScreen() {
        this.hideAllScreens();
        document.getElementById('homeScreen').classList.add('active');
        this.currentState = GAME_STATES.HOME;
        
        // Update player count (simulated)
        this.updatePlayerCount();
    }

    // Show game screen
    showGameScreen() {
        this.hideAllScreens();
        document.getElementById('gameScreen').classList.add('active');
        this.currentState = GAME_STATES.PLAYING;
        
        // Start game music
        if (audioManager) {
            audioManager.playMusic('gameplay');
        }
    }

    // Hide all screens
    hideAllScreens() {
        const screens = document.querySelectorAll('.screen');
        screens.forEach(screen => screen.classList.remove('active'));
    }

    // Hide loading screen
    hideLoadingScreen() {
        document.getElementById('loadingScreen').classList.remove('active');
    }

    // Show error message
    showError(message) {
        const loadingText = document.getElementById('loadingText');
        if (loadingText) {
            loadingText.textContent = 'Error: ' + message;
            loadingText.style.color = '#ff4444';
        }
    }

    // Update player count (simulated)
    updatePlayerCount() {
        const playerCount = document.getElementById('playerCount');
        if (playerCount) {
            // Simulate player count
            const count = Math.floor(Math.random() * 50) + 10;
            playerCount.textContent = count;
        }
    }

    // Delay function
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Start the game
    startGame() {
        if (this.game) {
            this.showGameScreen();
            this.game.resume();
        }
    }

    // Pause the game
    pauseGame() {
        if (this.game) {
            this.game.pause();
        }
    }

    // Resume the game
    resumeGame() {
        if (this.game) {
            this.game.resume();
        }
    }

    // Restart the game
    restartGame() {
        if (this.game) {
            this.game.restart();
        }
    }
}

// Game Class
class Game {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.camera = null;
        this.world = null;
        this.player = null;
        this.ui = null;
        this.chat = null;
        this.settings = null;
        
        // Game state
        this.isRunning = false;
        this.isPaused = false;
        this.lastTime = 0;
        this.deltaTime = 0;
        
        // Performance
        this.fps = 0;
        this.frameCount = 0;
        this.lastFpsTime = 0;
        
        this.init();
    }

    // Initialize the game
    init() {
        this.setupCanvas();
        this.setupCamera();
        this.setupWorld();
        this.setupPlayer();
        this.setupEventListeners();
    }

    // Setup canvas
    setupCanvas() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        
        // Set canvas size
        this.resizeCanvas();
        
        // Setup input manager with canvas
        if (inputManager) {
            inputManager.init(this.canvas);
        }
    }

    // Setup camera
    setupCamera() {
        this.camera = {
            x: 0,
            y: 0,
            width: this.canvas.width,
            height: this.canvas.height,
            zoom: 1.0,
            targetX: 0,
            targetY: 0,
            smoothness: 0.1
        };
    }

    // Setup world
    setupWorld() {
        this.world = new World();
        this.world.init();
    }

    // Setup player
    setupPlayer() {
        // Create player at center of world
        const spawnX = GAME_CONFIG.WORLD_SIZE / 2;
        const spawnY = GAME_CONFIG.WORLD_SIZE / 2;
        
        this.player = new Player(spawnX, spawnY);
        this.world.addEntity(this.player);
        
        // Set camera to follow player
        this.camera.targetX = this.player.x;
        this.camera.targetY = this.player.y;
    }

    // Setup event listeners
    setupEventListeners() {
        // Window resize
        window.addEventListener('resize', () => {
            this.resizeCanvas();
        });
        
        // Mouse wheel for zoom
        this.canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            const delta = e.deltaY > 0 ? -0.1 : 0.1;
            this.camera.zoom = Utils.clamp(this.camera.zoom + delta, 0.5, 2.0);
        });
        
        // Home screen buttons
        const playButton = document.getElementById('playButton');
        if (playButton) {
            playButton.addEventListener('click', () => {
                this.start();
            });
        }
        
        const settingsButton = document.getElementById('settingsButton');
        if (settingsButton) {
            settingsButton.addEventListener('click', () => {
                this.showSettings();
            });
        }
        
        const helpButton = document.getElementById('helpButton');
        if (helpButton) {
            helpButton.addEventListener('click', () => {
                this.showHelp();
            });
        }
    }

    // Initialize UI
    initUI() {
        this.ui = new UIManager(this);
        this.chat = new ChatManager(this);
        this.settings = new SettingsManager(this);
    }

    // Resize canvas
    resizeCanvas() {
        const container = this.canvas.parentElement;
        this.canvas.width = container.clientWidth;
        this.canvas.height = container.clientHeight;
        
        // Update camera
        this.camera.width = this.canvas.width;
        this.camera.height = this.canvas.height;
    }

    // Start the game
    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.isPaused = false;
        this.lastTime = performance.now();
        
        // Start game loop
        this.gameLoop();
        
        // Play game music
        if (audioManager) {
            audioManager.playMusic('gameplay');
        }
        
        console.log('Game started');
    }

    // Game loop
    gameLoop(currentTime = performance.now()) {
        if (!this.isRunning) return;
        
        // Calculate delta time
        this.deltaTime = (currentTime - this.lastTime) / 1000;
        this.lastTime = currentTime;
        
        // Cap delta time to prevent large jumps
        this.deltaTime = Math.min(this.deltaTime, 0.1);
        
        if (!this.isPaused) {
            // Update game systems
            this.update(this.deltaTime);
        }
        
        // Render game
        this.render();
        
        // Update FPS counter
        this.updateFPS(currentTime);
        
        // Continue game loop
        requestAnimationFrame((time) => this.gameLoop(time));
    }

    // Update game systems
    update(deltaTime) {
        // Update input
        if (inputManager) {
            inputManager.update();
        }
        
        // Update world
        if (this.world) {
            this.world.update(deltaTime);
        }
        
        // Update camera
        this.updateCamera(deltaTime);
        
        // Update UI
        if (this.ui) {
            this.ui.update(deltaTime);
        }
        
        // Update performance
        Utils.performance.update(performance.now());
    }

    // Update camera
    updateCamera(deltaTime) {
        if (this.player) {
            // Smooth camera following
            this.camera.targetX = this.player.x;
            this.camera.targetY = this.player.y;
            
            this.camera.x = Utils.lerp(this.camera.x, this.camera.targetX, this.camera.smoothness);
            this.camera.y = Utils.lerp(this.camera.y, this.camera.targetY, this.camera.smoothness);
            
            // Update input manager camera offset
            if (inputManager) {
                inputManager.setWorldOffset(this.camera.x, this.camera.y);
                inputManager.setZoom(this.camera.zoom);
            }
        }
    }

    // Render game
    render() {
        // Clear canvas
        this.ctx.fillStyle = '#0a0a0a';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Save context
        this.ctx.save();
        
        // Apply camera transform
        this.ctx.translate(this.canvas.width / 2, this.canvas.height / 2);
        this.ctx.scale(this.camera.zoom, this.camera.zoom);
        this.ctx.translate(-this.camera.x, -this.camera.y);
        
        // Render world
        if (this.world) {
            this.world.render(this.ctx, this.camera);
        }
        
        // Restore context
        this.ctx.restore();
        
        // Render UI overlay
        if (this.ui) {
            this.ui.render(this.ctx);
        }
    }

    // Update FPS counter
    updateFPS(currentTime) {
        this.frameCount++;
        
        if (currentTime - this.lastFpsTime >= 1000) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.lastFpsTime = currentTime;
        }
    }

    // Pause game
    pause() {
        this.isPaused = true;
        console.log('Game paused');
    }

    // Resume game
    resume() {
        this.isPaused = false;
        console.log('Game resumed');
    }

    // Restart game
    restart() {
        // Reset player position
        if (this.player) {
            this.player.x = GAME_CONFIG.WORLD_SIZE / 2;
            this.player.y = GAME_CONFIG.WORLD_SIZE / 2;
            this.player.health = this.player.maxHealth;
            this.player.hunger = this.player.maxHunger;
            this.player.thirst = this.player.maxThirst;
        }
        
        // Clear world entities (except player)
        if (this.world) {
            this.world.clearEntities();
            this.world.addEntity(this.player);
        }
        
        console.log('Game restarted');
    }

    // Handle left click
    handleLeftClick(x, y) {
        if (!this.player) return;
        
        // Convert screen coordinates to world coordinates
        const worldX = (x - this.canvas.width / 2) / this.camera.zoom + this.camera.x;
        const worldY = (y - this.canvas.height / 2) / this.camera.zoom + this.camera.y;
        
        // Find entity at click position
        const entity = this.world.getEntityAt(worldX, worldY);
        
        if (entity) {
            // Interact with entity
            this.player.interact(entity);
        } else {
            // Move player to position
            this.player.setTarget(worldX, worldY);
        }
    }

    // Handle right click
    handleRightClick(x, y) {
        if (!this.player) return;
        
        // Convert screen coordinates to world coordinates
        const worldX = (x - this.canvas.width / 2) / this.camera.zoom + this.camera.x;
        const worldY = (y - this.canvas.height / 2) / this.camera.zoom + this.camera.y;
        
        // Attack at position
        this.player.attack(this.world.getEntityAt(worldX, worldY));
    }

    // Handle middle click
    handleMiddleClick(x, y) {
        // Place building if in building mode
        if (this.player && this.player.buildingMode) {
            const worldX = (x - this.canvas.width / 2) / this.camera.zoom + this.camera.x;
            const worldY = (y - this.canvas.height / 2) / this.camera.zoom + this.camera.y;
            
            this.player.placeBuilding();
        }
    }

    // Handle touch events
    handleTouchStart(x, y) {
        this.handleLeftClick(x, y);
    }

    handleTouchMove(x, y) {
        // Handle touch movement
    }

    handleTouchEnd() {
        // Handle touch end
    }

    // Handle mouse drag
    handleMouseDrag(x, y) {
        // Handle mouse drag
    }

    // Handle touch drag
    handleTouchDrag(x, y) {
        // Handle touch drag
    }

    // Toggle pause
    togglePause() {
        if (this.isPaused) {
            this.resume();
        } else {
            this.pause();
        }
    }

    // Show settings
    showSettings() {
        if (this.settings) {
            this.settings.show();
        }
    }

    // Show help
    showHelp() {
        alert('How to Play:\n\n' +
              'WASD - Move\n' +
              'Left Click - Interact/Attack\n' +
              'I - Inventory\n' +
              'B - Building Menu\n' +
              'ESC - Pause\n' +
              'Shift - Sprint\n\n' +
              'Gather resources, craft tools, build structures, and survive!');
    }
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    // Create global game manager
    window.gameManager = new GameManager();
    
    // Make game globally accessible
    window.game = null;
    
    // Initialize when everything is loaded
    window.addEventListener('load', () => {
        console.log('Survival.io - Game Ready');
    });
});

// Handle page visibility changes
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        // Pause game when tab is not visible
        if (window.game) {
            game.pause();
        }
    } else {
        // Resume game when tab becomes visible
        if (window.game) {
            game.resume();
        }
    }
});

// Handle before unload
window.addEventListener('beforeunload', () => {
    // Save game state
    if (window.game && window.game.player) {
        const gameState = {
            player: window.game.player.serialize(),
            world: window.game.world.serialize()
        };
        
        Utils.storage.set('gameState', gameState);
    }
    
    // Save audio settings
    if (window.audioManager) {
        audioManager.saveSettings();
    }
}); 
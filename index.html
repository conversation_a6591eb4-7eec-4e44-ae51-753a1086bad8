<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Survival.io - Multiplayer Survival Game</title>
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div id="gameContainer">
        <!-- Home Screen -->
        <div id="homeScreen" class="screen active">
            <div class="home-content">
                <h1 class="game-title">Survival.io</h1>
                <div class="menu-buttons">
                    <button id="playButton" class="menu-btn primary">Play Game</button>
                    <button id="settingsButton" class="menu-btn">Settings</button>
                    <button id="helpButton" class="menu-btn">How to Play</button>
                </div>
                <div class="game-stats">
                    <p>Players Online: <span id="playerCount">0</span></p>
                    <p>Servers: <span id="serverCount">1</span></p>
                </div>
            </div>
        </div>

        <!-- Game Screen -->
        <div id="gameScreen" class="screen">
            <canvas id="gameCanvas"></canvas>
            
            <!-- UI Overlay -->
            <div id="gameUI">
                <!-- Top HUD -->
                <div id="topHUD">
                    <div class="health-bar">
                        <div class="health-fill" id="healthFill"></div>
                        <span class="health-text">100/100</span>
                    </div>
                    <div class="hunger-bar">
                        <div class="hunger-fill" id="hungerFill"></div>
                        <span class="hunger-text">100/100</span>
                    </div>
                    <div class="level-info">
                        <span>Level: <span id="playerLevel">1</span></span>
                        <span>XP: <span id="playerXP">0</span>/<span id="nextLevelXP">100</span></span>
                    </div>
                </div>

                <!-- Inventory -->
                <div id="inventory" class="inventory-panel">
                    <div class="inventory-header">
                        <h3>Inventory</h3>
                        <button id="closeInventory" class="close-btn">×</button>
                    </div>
                    <div class="inventory-grid" id="inventoryGrid">
                        <!-- Inventory slots will be generated here -->
                    </div>
                    <div class="crafting-section">
                        <h4>Crafting</h4>
                        <div class="crafting-grid" id="craftingGrid">
                            <!-- Crafting recipes will be here -->
                        </div>
                    </div>
                </div>

                <!-- Building Menu -->
                <div id="buildingMenu" class="building-panel">
                    <div class="building-header">
                        <h3>Build</h3>
                        <button id="closeBuilding" class="close-btn">×</button>
                    </div>
                    <div class="building-grid" id="buildingGrid">
                        <!-- Building options will be here -->
                    </div>
                </div>

                <!-- Minimap -->
                <div id="minimap" class="minimap">
                    <canvas id="minimapCanvas"></canvas>
                </div>

                <!-- Chat -->
                <div id="chat" class="chat-panel">
                    <div id="chatMessages" class="chat-messages"></div>
                    <div class="chat-input-container">
                        <input type="text" id="chatInput" placeholder="Type to chat..." maxlength="100">
                        <button id="sendChat">Send</button>
                    </div>
                </div>

                <!-- Settings Panel -->
                <div id="settingsPanel" class="settings-panel">
                    <div class="settings-header">
                        <h3>Settings</h3>
                        <button id="closeSettings" class="close-btn">×</button>
                    </div>
                    <div class="settings-content">
                        <div class="setting-group">
                            <label>Music Volume</label>
                            <input type="range" id="musicVolume" min="0" max="100" value="50">
                        </div>
                        <div class="setting-group">
                            <label>SFX Volume</label>
                            <input type="range" id="sfxVolume" min="0" max="100" value="70">
                        </div>
                        <div class="setting-group">
                            <label>Graphics Quality</label>
                            <select id="graphicsQuality">
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                            </select>
                        </div>
                        <button id="saveSettings" class="save-btn">Save Settings</button>
                    </div>
                </div>
            </div>

            <!-- Game Controls -->
            <div id="gameControls">
                <button id="inventoryBtn" class="control-btn">I</button>
                <button id="buildingBtn" class="control-btn">B</button>
                <button id="settingsBtn" class="control-btn">⚙</button>
                <button id="escapeBtn" class="control-btn">ESC</button>
            </div>
        </div>

        <!-- Loading Screen -->
        <div id="loadingScreen" class="screen">
            <div class="loading-content">
                <h2>Loading...</h2>
                <div class="loading-bar">
                    <div class="loading-fill" id="loadingFill"></div>
                </div>
                <p id="loadingText">Initializing game...</p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/constants.js"></script>
    <script src="js/audio.js"></script>
    <script src="js/input.js"></script>
    <script src="js/entities/entity.js"></script>
    <script src="js/entities/player.js"></script>
    <script src="js/entities/npc.js"></script>
    <script src="js/entities/resource.js"></script>
    <script src="js/entities/building.js"></script>
    <script src="js/entities/projectile.js"></script>
    <script src="js/world/world.js"></script>
    <script src="js/world/minimap.js"></script>
    <script src="js/ui/inventory.js"></script>
    <script src="js/ui/building.js"></script>
    <script src="js/ui/chat.js"></script>
    <script src="js/ui/settings.js"></script>
    <script src="js/game.js"></script>
    <script src="js/main.js"></script>
</body>
</html> 
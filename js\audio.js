// Audio System
class AudioManager {
    constructor() {
        this.sounds = {};
        this.music = {};
        this.currentMusic = null;
        this.musicVolume = 0.5;
        this.sfxVolume = 0.7;
        this.masterVolume = 1.0;
        this.enabled = true;
        
        this.loadSounds();
    }

    // Load all sound effects
    loadSounds() {
        // Create audio contexts for different sound types
        this.sounds = {
            // Player sounds
            player_hit: this.createSound('hit', 0.3),
            player_death: this.createSound('death', 0.5),
            player_levelup: this.createSound('levelup', 0.4),
            player_eat: this.createSound('eat', 0.2),
            player_drink: this.createSound('drink', 0.2),
            
            // Combat sounds
            sword_swing: this.createSound('sword_swing', 0.4),
            bow_shoot: this.createSound('bow_shoot', 0.3),
            arrow_hit: this.createSound('arrow_hit', 0.3),
            damage: this.createSound('damage', 0.3),
            
            // Resource sounds
            tree_chop: this.createSound('tree_chop', 0.3),
            rock_mine: this.createSound('rock_mine', 0.3),
            resource_collect: this.createSound('collect', 0.2),
            
            // Building sounds
            build_start: this.createSound('build_start', 0.3),
            build_complete: this.createSound('build_complete', 0.4),
            build_destroy: this.createSound('build_destroy', 0.4),
            
            // UI sounds
            button_click: this.createSound('click', 0.2),
            inventory_open: this.createSound('inventory_open', 0.2),
            inventory_close: this.createSound('inventory_close', 0.2),
            notification: this.createSound('notification', 0.3),
            
            // Environment sounds
            footsteps: this.createSound('footsteps', 0.1),
            wind: this.createSound('wind', 0.1),
            water: this.createSound('water', 0.2)
        };

        // Create music tracks
        this.music = {
            menu: this.createMusic('menu', 0.3),
            gameplay: this.createMusic('gameplay', 0.4),
            combat: this.createMusic('combat', 0.5),
            ambient: this.createMusic('ambient', 0.2)
        };
    }

    // Create a simple sound effect using Web Audio API
    createSound(name, volume = 0.5) {
        return {
            name: name,
            volume: volume,
            play: (pitch = 1.0, pan = 0.0) => {
                if (!this.enabled) return;
                
                try {
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();
                    const panNode = audioContext.createStereoPanner();
                    
                    // Set up audio chain
                    oscillator.connect(gainNode);
                    gainNode.connect(panNode);
                    panNode.connect(audioContext.destination);
                    
                    // Configure sound based on name
                    this.configureSound(oscillator, gainNode, panNode, name, pitch, pan);
                    
                    // Play the sound
                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.1);
                    
                } catch (e) {
                    console.warn('Audio not supported:', e);
                }
            }
        };
    }

    // Configure sound parameters based on sound type
    configureSound(oscillator, gainNode, panNode, name, pitch, pan) {
        const baseVolume = this.sounds[name]?.volume || 0.5;
        const finalVolume = baseVolume * this.sfxVolume * this.masterVolume;
        
        gainNode.gain.setValueAtTime(finalVolume, gainNode.context.currentTime);
        panNode.pan.setValueAtTime(pan, panNode.context.currentTime);
        
        switch (name) {
            case 'hit':
            case 'damage':
                oscillator.type = 'sawtooth';
                oscillator.frequency.setValueAtTime(200 * pitch, oscillator.context.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(50 * pitch, oscillator.context.currentTime + 0.1);
                break;
                
            case 'sword_swing':
                oscillator.type = 'sine';
                oscillator.frequency.setValueAtTime(300 * pitch, oscillator.context.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(100 * pitch, oscillator.context.currentTime + 0.2);
                break;
                
            case 'bow_shoot':
                oscillator.type = 'sine';
                oscillator.frequency.setValueAtTime(400 * pitch, oscillator.context.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(200 * pitch, oscillator.context.currentTime + 0.15);
                break;
                
            case 'tree_chop':
            case 'rock_mine':
                oscillator.type = 'square';
                oscillator.frequency.setValueAtTime(150 * pitch, oscillator.context.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(75 * pitch, oscillator.context.currentTime + 0.1);
                break;
                
            case 'collect':
            case 'eat':
            case 'drink':
                oscillator.type = 'sine';
                oscillator.frequency.setValueAtTime(800 * pitch, oscillator.context.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(400 * pitch, oscillator.context.currentTime + 0.1);
                break;
                
            case 'levelup':
                oscillator.type = 'sine';
                oscillator.frequency.setValueAtTime(400 * pitch, oscillator.context.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(800 * pitch, oscillator.context.currentTime + 0.2);
                break;
                
            case 'click':
            case 'notification':
                oscillator.type = 'sine';
                oscillator.frequency.setValueAtTime(600 * pitch, oscillator.context.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(300 * pitch, oscillator.context.currentTime + 0.05);
                break;
                
            case 'footsteps':
                oscillator.type = 'noise';
                gainNode.gain.setValueAtTime(finalVolume * 0.3, gainNode.context.currentTime);
                break;
                
            default:
                oscillator.type = 'sine';
                oscillator.frequency.setValueAtTime(440 * pitch, oscillator.context.currentTime);
        }
    }

    // Create background music
    createMusic(name, volume = 0.5) {
        return {
            name: name,
            volume: volume,
            isPlaying: false,
            audioContext: null,
            oscillators: [],
            
            play: () => {
                if (!this.enabled || this.isPlaying) return;
                
                try {
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    this.isPlaying = true;
                    
                    // Create ambient music using multiple oscillators
                    this.createAmbientMusic();
                    
                } catch (e) {
                    console.warn('Audio not supported:', e);
                }
            },
            
            stop: () => {
                if (this.oscillators.length > 0) {
                    this.oscillators.forEach(osc => {
                        try {
                            osc.stop();
                        } catch (e) {
                            // Oscillator already stopped
                        }
                    });
                    this.oscillators = [];
                }
                this.isPlaying = false;
            },
            
            setVolume: (volume) => {
                this.volume = volume;
                // Update volume for all active oscillators
                this.oscillators.forEach(osc => {
                    if (osc.gainNode) {
                        osc.gainNode.gain.setValueAtTime(
                            volume * this.musicVolume * this.masterVolume,
                            osc.gainNode.context.currentTime
                        );
                    }
                });
            }
        };
    }

    // Create ambient background music
    createAmbientMusic() {
        const music = this.music[this.currentMusic];
        if (!music || !music.audioContext) return;
        
        const ctx = music.audioContext;
        
        // Create multiple oscillators for rich sound
        const frequencies = [220, 330, 440, 550]; // A major chord
        const volumes = [0.1, 0.05, 0.08, 0.03];
        
        frequencies.forEach((freq, index) => {
            const oscillator = ctx.createOscillator();
            const gainNode = ctx.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(ctx.destination);
            
            oscillator.type = 'sine';
            oscillator.frequency.setValueAtTime(freq, ctx.currentTime);
            
            // Add slight variation to create movement
            oscillator.frequency.setValueAtTime(freq * 1.01, ctx.currentTime + 2);
            oscillator.frequency.setValueAtTime(freq * 0.99, ctx.currentTime + 4);
            oscillator.frequency.setValueAtTime(freq, ctx.currentTime + 6);
            
            gainNode.gain.setValueAtTime(
                volumes[index] * music.volume * this.musicVolume * this.masterVolume,
                ctx.currentTime
            );
            
            oscillator.start();
            music.oscillators.push({ oscillator, gainNode });
        });
    }

    // Play a sound effect
    playSound(soundName, pitch = 1.0, pan = 0.0) {
        if (this.sounds[soundName]) {
            this.sounds[soundName].play(pitch, pan);
        }
    }

    // Play music track
    playMusic(trackName) {
        if (this.currentMusic && this.music[this.currentMusic]) {
            this.music[this.currentMusic].stop();
        }
        
        this.currentMusic = trackName;
        
        if (this.music[trackName]) {
            this.music[trackName].play();
        }
    }

    // Stop current music
    stopMusic() {
        if (this.currentMusic && this.music[this.currentMusic]) {
            this.music[this.currentMusic].stop();
            this.currentMusic = null;
        }
    }

    // Set music volume
    setMusicVolume(volume) {
        this.musicVolume = Utils.clamp(volume, 0, 1);
        
        // Update all music tracks
        Object.values(this.music).forEach(track => {
            track.setVolume(track.volume);
        });
    }

    // Set SFX volume
    setSFXVolume(volume) {
        this.sfxVolume = Utils.clamp(volume, 0, 1);
    }

    // Set master volume
    setMasterVolume(volume) {
        this.masterVolume = Utils.clamp(volume, 0, 1);
        
        // Update music volume
        this.setMusicVolume(this.musicVolume);
    }

    // Enable/disable audio
    setEnabled(enabled) {
        this.enabled = enabled;
        
        if (!enabled) {
            this.stopMusic();
        }
    }

    // Load audio settings from storage
    loadSettings() {
        const settings = Utils.storage.get('audioSettings', {
            musicVolume: 0.5,
            sfxVolume: 0.7,
            masterVolume: 1.0,
            enabled: true
        });
        
        this.setMusicVolume(settings.musicVolume);
        this.setSFXVolume(settings.sfxVolume);
        this.setMasterVolume(settings.masterVolume);
        this.setEnabled(settings.enabled);
    }

    // Save audio settings to storage
    saveSettings() {
        const settings = {
            musicVolume: this.musicVolume,
            sfxVolume: this.sfxVolume,
            masterVolume: this.masterVolume,
            enabled: this.enabled
        };
        
        Utils.storage.set('audioSettings', settings);
    }

    // Play footsteps based on movement
    playFootsteps(isMoving, surface = 'grass') {
        if (!isMoving) return;
        
        // Throttle footsteps to avoid spam
        if (!this.footstepTimer) {
            this.footstepTimer = Date.now();
        }
        
        if (Date.now() - this.footstepTimer > 300) {
            this.playSound('footsteps', 1.0, 0.0);
            this.footstepTimer = Date.now();
        }
    }

    // Play combat sounds
    playCombatSound(weaponType, hit = false) {
        switch (weaponType) {
            case 'sword':
                this.playSound('sword_swing');
                if (hit) this.playSound('damage');
                break;
            case 'bow':
                this.playSound('bow_shoot');
                if (hit) this.playSound('arrow_hit');
                break;
            case 'axe':
                this.playSound('tree_chop');
                if (hit) this.playSound('damage');
                break;
            case 'pickaxe':
                this.playSound('rock_mine');
                if (hit) this.playSound('damage');
                break;
        }
    }

    // Play resource collection sounds
    playResourceSound(resourceType) {
        switch (resourceType) {
            case 'tree':
                this.playSound('tree_chop');
                break;
            case 'rock':
                this.playSound('rock_mine');
                break;
            case 'berry_bush':
            case 'water_source':
                this.playSound('collect');
                break;
        }
    }

    // Play UI sounds
    playUISound(action) {
        switch (action) {
            case 'click':
                this.playSound('button_click');
                break;
            case 'inventory_open':
                this.playSound('inventory_open');
                break;
            case 'inventory_close':
                this.playSound('inventory_close');
                break;
            case 'notification':
                this.playSound('notification');
                break;
        }
    }
}

// Global audio manager instance
let audioManager; 
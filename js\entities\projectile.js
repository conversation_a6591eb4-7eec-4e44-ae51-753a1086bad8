// Projectile Class
class Projectile extends Entity {
    constructor(startX, startY, targetX, targetY, damage, source, config = {}) {
        super(startX, startY, 'projectile', {
            width: 8,
            height: 8,
            health: 1,
            maxHealth: 1,
            speed: 0,
            damage: damage,
            defense: 0,
            color: '#ffff00',
            collisionRadius: 4,
            solid: false,
            renderLayer: 8,
            ...config
        });

        this.targetX = targetX;
        this.targetY = targetY;
        this.source = source;
        this.damage = damage;
        
        // Movement
        this.speed = 300; // pixels per second
        this.maxDistance = 500;
        this.distanceTraveled = 0;
        this.startX = startX;
        this.startY = startY;
        
        // Calculate direction
        const dx = targetX - startX;
        const dy = targetY - startY;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance > 0) {
            this.vx = (dx / distance) * this.speed;
            this.vy = (dy / distance) * this.speed;
        } else {
            this.vx = 0;
            this.vy = this.speed;
        }
        
        // Projectile properties
        this.projectileType = config.projectileType || 'arrow';
        this.piercing = config.piercing || false;
        this.hitEntities = new Set();
        this.lifeTime = 3000; // 3 seconds
        this.spawnTime = Date.now();
        
        // Visual effects
        this.trail = [];
        this.maxTrailLength = 10;
        
        // Tags
        this.addTag('projectile');
        this.addTag('damaging');
    }

    // Update projectile logic
    update(deltaTime) {
        super.update(deltaTime);
        
        this.updateMovement(deltaTime);
        this.updateTrail(deltaTime);
        this.updateLifetime(deltaTime);
        this.updateCollision(deltaTime);
    }

    // Update movement
    updateMovement(deltaTime) {
        // Move projectile
        const moveX = this.vx * deltaTime;
        const moveY = this.vy * deltaTime;
        
        this.x += moveX;
        this.y += moveY;
        
        // Update distance traveled
        const dx = this.x - this.startX;
        const dy = this.y - this.startY;
        this.distanceTraveled = Math.sqrt(dx * dx + dy * dy);
        
        // Check if projectile has traveled too far
        if (this.distanceTraveled >= this.maxDistance) {
            this.destroy();
        }
    }

    // Update trail effect
    updateTrail(deltaTime) {
        // Add current position to trail
        this.trail.push({
            x: this.x,
            y: this.y,
            life: 1.0
        });
        
        // Remove old trail points
        if (this.trail.length > this.maxTrailLength) {
            this.trail.shift();
        }
        
        // Update trail life
        for (let i = 0; i < this.trail.length; i++) {
            this.trail[i].life -= deltaTime * 0.001; // Fade out over 1 second
        }
        
        // Remove dead trail points
        this.trail = this.trail.filter(point => point.life > 0);
    }

    // Update lifetime
    updateLifetime(deltaTime) {
        if (Date.now() - this.spawnTime > this.lifeTime) {
            this.destroy();
        }
    }

    // Update collision detection
    updateCollision(deltaTime) {
        if (!window.game || !game.world) return;
        
        const nearbyEntities = game.world.getEntitiesInRange(this.x, this.y, this.collisionRadius);
        
        for (const entity of nearbyEntities) {
            if (entity === this || entity === this.source) continue;
            if (this.hitEntities.has(entity.id)) continue;
            
            // Check if entity can be hit
            if (this.canHitEntity(entity)) {
                this.hitEntity(entity);
            }
        }
    }

    // Check if projectile can hit entity
    canHitEntity(entity) {
        // Don't hit the source
        if (entity === this.source) return false;
        
        // Don't hit friendly entities
        if (this.source && entity.hasTag('player') && this.source.hasTag('player')) {
            return false; // Friendly fire disabled for now
        }
        
        // Don't hit projectiles
        if (entity.hasTag('projectile')) return false;
        
        // Don't hit resources (unless it's a special projectile)
        if (entity.hasTag('resource') && this.projectileType !== 'explosive') return false;
        
        return true;
    }

    // Hit entity
    hitEntity(entity) {
        // Apply damage
        const killed = entity.takeDamage(this.damage, this.source);
        
        // Add to hit entities
        this.hitEntities.add(entity.id);
        
        // Create hit effect
        this.createHitEffect(entity);
        
        // Play hit sound
        if (window.audioManager) {
            audioManager.playSound('arrow_hit');
        }
        
        // Destroy projectile if not piercing
        if (!this.piercing) {
            this.destroy();
        }
        
        return killed;
    }

    // Create hit effect
    createHitEffect(entity) {
        for (let i = 0; i < 5; i++) {
            const particle = {
                x: this.x + Utils.random(-5, 5),
                y: this.y + Utils.random(-5, 5),
                vx: Utils.random(-2, 2),
                vy: Utils.random(-2, 2),
                life: 0.5,
                maxLife: 0.5,
                color: '#ffff00',
                size: Utils.random(2, 4),
                update: function(deltaTime) {
                    this.x += this.vx;
                    this.y += this.vy;
                    this.vx *= 0.95; // Friction
                    this.vy *= 0.95;
                    this.life -= deltaTime;
                    
                    if (this.life <= 0) {
                        this.dead = true;
                    }
                },
                render: function(ctx) {
                    const alpha = this.life / this.maxLife;
                    ctx.save();
                    ctx.globalAlpha = alpha;
                    ctx.fillStyle = this.color;
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.restore();
                }
            };
            
            this.particles.push(particle);
        }
    }

    // Destroy projectile
    destroy() {
        this.dead = true;
        this.active = false;
        
        // Create destruction particles
        this.createDestructionParticles();
    }

    // Create destruction particles
    createDestructionParticles() {
        for (let i = 0; i < 8; i++) {
            const particle = {
                x: this.x + Utils.random(-3, 3),
                y: this.y + Utils.random(-3, 3),
                vx: Utils.random(-2, 2),
                vy: Utils.random(-2, 2),
                life: 0.3,
                maxLife: 0.3,
                color: this.color,
                size: Utils.random(1, 3),
                update: function(deltaTime) {
                    this.x += this.vx;
                    this.y += this.vy;
                    this.vx *= 0.9; // Friction
                    this.vy *= 0.9;
                    this.life -= deltaTime;
                    
                    if (this.life <= 0) {
                        this.dead = true;
                    }
                },
                render: function(ctx) {
                    const alpha = this.life / this.maxLife;
                    ctx.save();
                    ctx.globalAlpha = alpha;
                    ctx.fillStyle = this.color;
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.restore();
                }
            };
            
            this.particles.push(particle);
        }
    }

    // Override render method
    renderEntity(ctx) {
        // Draw trail
        this.renderTrail(ctx);
        
        // Draw projectile
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.width / 2, 0, Math.PI * 2);
        ctx.fill();
        
        // Draw projectile icon
        ctx.fillStyle = '#ffffff';
        ctx.font = '8px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(this.getProjectileIcon(), 0, 2);
    }

    // Render trail
    renderTrail(ctx) {
        for (let i = 0; i < this.trail.length; i++) {
            const point = this.trail[i];
            const alpha = point.life * 0.5;
            
            ctx.save();
            ctx.globalAlpha = alpha;
            ctx.fillStyle = this.color;
            ctx.beginPath();
            ctx.arc(point.x, point.y, 2, 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();
        }
    }

    // Get projectile icon
    getProjectileIcon() {
        switch (this.projectileType) {
            case 'arrow': return '🏹';
            case 'bullet': return '•';
            case 'fireball': return '🔥';
            case 'laser': return '⚡';
            default: return '•';
        }
    }

    // Set projectile type
    setProjectileType(type) {
        this.projectileType = type;
        
        // Update properties based on type
        switch (type) {
            case 'arrow':
                this.color = '#8B4513';
                this.maxDistance = 400;
                this.speed = 250;
                break;
            case 'bullet':
                this.color = '#FFD700';
                this.maxDistance = 300;
                this.speed = 400;
                break;
            case 'fireball':
                this.color = '#FF4500';
                this.maxDistance = 200;
                this.speed = 150;
                this.piercing = true;
                break;
            case 'laser':
                this.color = '#00FFFF';
                this.maxDistance = 600;
                this.speed = 500;
                this.piercing = true;
                break;
        }
    }

    // Set piercing
    setPiercing(piercing) {
        this.piercing = piercing;
    }

    // Set damage
    setDamage(damage) {
        this.damage = damage;
    }

    // Set speed
    setSpeed(speed) {
        this.speed = speed;
        
        // Recalculate velocity
        const dx = this.targetX - this.startX;
        const dy = this.targetY - this.startY;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance > 0) {
            this.vx = (dx / distance) * this.speed;
            this.vy = (dy / distance) * this.speed;
        }
    }

    // Set max distance
    setMaxDistance(distance) {
        this.maxDistance = distance;
    }

    // Serialize projectile data
    serialize() {
        const data = super.serialize();
        return {
            ...data,
            targetX: this.targetX,
            targetY: this.targetY,
            source: this.source ? this.source.id : null,
            damage: this.damage,
            projectileType: this.projectileType,
            piercing: this.piercing,
            hitEntities: Array.from(this.hitEntities),
            distanceTraveled: this.distanceTraveled,
            maxDistance: this.maxDistance,
            speed: this.speed,
            spawnTime: this.spawnTime
        };
    }

    // Deserialize projectile data
    deserialize(data) {
        super.deserialize(data);
        this.targetX = data.targetX;
        this.targetY = data.targetY;
        this.source = data.source;
        this.damage = data.damage;
        this.projectileType = data.projectileType || 'arrow';
        this.piercing = data.piercing || false;
        this.hitEntities = new Set(data.hitEntities || []);
        this.distanceTraveled = data.distanceTraveled || 0;
        this.maxDistance = data.maxDistance || 500;
        this.speed = data.speed || 300;
        this.spawnTime = data.spawnTime || Date.now();
    }
}

// Projectile Factory
class ProjectileFactory {
    static createArrow(startX, startY, targetX, targetY, damage, source) {
        return new Projectile(startX, startY, targetX, targetY, damage, source, {
            projectileType: 'arrow',
            piercing: false,
            color: '#8B4513',
            maxDistance: 400,
            speed: 250
        });
    }

    static createBullet(startX, startY, targetX, targetY, damage, source) {
        return new Projectile(startX, startY, targetX, targetY, damage, source, {
            projectileType: 'bullet',
            piercing: false,
            color: '#FFD700',
            maxDistance: 300,
            speed: 400
        });
    }

    static createFireball(startX, startY, targetX, targetY, damage, source) {
        return new Projectile(startX, startY, targetX, targetY, damage, source, {
            projectileType: 'fireball',
            piercing: true,
            color: '#FF4500',
            maxDistance: 200,
            speed: 150
        });
    }

    static createLaser(startX, startY, targetX, targetY, damage, source) {
        return new Projectile(startX, startY, targetX, targetY, damage, source, {
            projectileType: 'laser',
            piercing: true,
            color: '#00FFFF',
            maxDistance: 600,
            speed: 500
        });
    }
} 
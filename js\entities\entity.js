// Base Entity Class
class Entity {
    constructor(x, y, type, config = {}) {
        this.id = Utils.generateId();
        this.x = x;
        this.y = y;
        this.type = type;
        this.width = config.width || 32;
        this.height = config.height || 32;
        this.health = config.health || 100;
        this.maxHealth = config.maxHealth || 100;
        this.speed = config.speed || 0;
        this.damage = config.damage || 0;
        this.defense = config.defense || 0;
        this.color = config.color || '#ffffff';
        this.visible = true;
        this.active = true;
        this.dead = false;
        
        // Movement
        this.vx = 0;
        this.vy = 0;
        this.targetX = x;
        this.targetY = y;
        
        // Animation
        this.animationFrame = 0;
        this.animationSpeed = 0.1;
        this.animationTimer = 0;
        
        // Effects
        this.effects = [];
        this.particles = [];
        
        // Collision
        this.collisionRadius = config.collisionRadius || this.width / 2;
        this.solid = config.solid !== false;
        
        // Rendering
        this.renderLayer = config.renderLayer || 0;
        this.opacity = 1.0;
        this.rotation = 0;
        this.scale = 1.0;
        
        // Timers
        this.timers = {};
        this.lastUpdate = Date.now();
        
        // Tags for identification
        this.tags = config.tags || [];
    }

    // Update entity logic
    update(deltaTime) {
        if (!this.active || this.dead) return;
        
        this.updateTimers(deltaTime);
        this.updateMovement(deltaTime);
        this.updateAnimation(deltaTime);
        this.updateEffects(deltaTime);
        this.updateParticles(deltaTime);
        
        this.lastUpdate = Date.now();
    }

    // Update movement
    updateMovement(deltaTime) {
        if (this.speed > 0) {
            // Move towards target
            const dx = this.targetX - this.x;
            const dy = this.targetY - this.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance > 1) {
                const speed = this.speed * deltaTime;
                const moveX = (dx / distance) * speed;
                const moveY = (dy / distance) * speed;
                
                this.x += moveX;
                this.y += moveY;
                
                this.vx = moveX / deltaTime;
                this.vy = moveY / deltaTime;
            } else {
                this.vx = 0;
                this.vy = 0;
            }
        }
    }

    // Update animation
    updateAnimation(deltaTime) {
        this.animationTimer += deltaTime;
        
        if (this.animationTimer >= this.animationSpeed) {
            this.animationFrame = (this.animationFrame + 1) % 4; // 4 frame animation
            this.animationTimer = 0;
        }
    }

    // Update effects
    updateEffects(deltaTime) {
        for (let i = this.effects.length - 1; i >= 0; i--) {
            const effect = this.effects[i];
            effect.duration -= deltaTime;
            
            if (effect.duration <= 0) {
                this.removeEffect(effect);
                this.effects.splice(i, 1);
            } else {
                effect.update(deltaTime, this);
            }
        }
    }

    // Update particles
    updateParticles(deltaTime) {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            particle.update(deltaTime);
            
            if (particle.dead) {
                this.particles.splice(i, 1);
            }
        }
    }

    // Update timers
    updateTimers(deltaTime) {
        for (const [name, timer] of Object.entries(this.timers)) {
            timer.time -= deltaTime;
            
            if (timer.time <= 0) {
                if (timer.callback) {
                    timer.callback();
                }
                
                if (timer.repeat) {
                    timer.time = timer.interval;
                } else {
                    delete this.timers[name];
                }
            }
        }
    }

    // Set target position
    setTarget(x, y) {
        this.targetX = x;
        this.targetY = y;
    }

    // Move entity
    move(x, y) {
        this.x = x;
        this.y = y;
    }

    // Move entity by offset
    moveBy(dx, dy) {
        this.x += dx;
        this.y += dy;
    }

    // Set velocity
    setVelocity(vx, vy) {
        this.vx = vx;
        this.vy = vy;
    }

    // Get distance to another entity
    getDistanceTo(entity) {
        const dx = entity.x - this.x;
        const dy = entity.y - this.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    // Get angle to another entity
    getAngleTo(entity) {
        return Math.atan2(entity.y - this.y, entity.x - this.x);
    }

    // Check collision with another entity
    collidesWith(entity) {
        const distance = this.getDistanceTo(entity);
        const combinedRadius = this.collisionRadius + entity.collisionRadius;
        return distance < combinedRadius;
    }

    // Check if point is inside entity
    containsPoint(x, y) {
        const dx = x - this.x;
        const dy = y - this.y;
        return (dx * dx + dy * dy) <= (this.collisionRadius * this.collisionRadius);
    }

    // Take damage
    takeDamage(amount, source = null) {
        if (this.dead) return false;
        
        const actualDamage = Math.max(1, amount - this.defense);
        this.health = Math.max(0, this.health - actualDamage);
        
        // Create damage particle
        this.createDamageParticle(actualDamage);
        
        // Play damage sound
        if (window.audioManager) {
            audioManager.playSound('damage');
        }
        
        if (this.health <= 0) {
            this.die();
            return true;
        }
        
        return false;
    }

    // Heal entity
    heal(amount) {
        if (this.dead) return false;
        
        this.health = Math.min(this.maxHealth, this.health + amount);
        return true;
    }

    // Die
    die() {
        if (this.dead) return;
        
        this.dead = true;
        this.active = false;
        this.health = 0;
        
        // Create death particles
        this.createDeathParticles();
        
        // Play death sound
        if (window.audioManager) {
            audioManager.playSound('player_death');
        }
        
        // Trigger death event
        this.onDeath();
    }

    // Revive entity
    revive(health = this.maxHealth) {
        this.dead = false;
        this.active = true;
        this.health = health;
    }

    // Add effect
    addEffect(effect) {
        this.effects.push(effect);
        effect.onApply(this);
    }

    // Remove effect
    removeEffect(effect) {
        effect.onRemove(this);
    }

    // Add timer
    addTimer(name, interval, callback, repeat = false) {
        this.timers[name] = {
            time: interval,
            interval: interval,
            callback: callback,
            repeat: repeat
        };
    }

    // Remove timer
    removeTimer(name) {
        delete this.timers[name];
    }

    // Create damage particle
    createDamageParticle(amount) {
        const particle = {
            x: this.x + Utils.random(-10, 10),
            y: this.y - 20,
            vx: Utils.random(-2, 2),
            vy: Utils.random(-3, -1),
            life: 1.0,
            maxLife: 1.0,
            color: '#ff4444',
            text: `-${amount}`,
            size: 12,
            update: function(deltaTime) {
                this.x += this.vx;
                this.y += this.vy;
                this.vy += 0.1; // Gravity
                this.life -= deltaTime;
                
                if (this.life <= 0) {
                    this.dead = true;
                }
            },
            render: function(ctx) {
                const alpha = this.life / this.maxLife;
                ctx.save();
                ctx.globalAlpha = alpha;
                ctx.fillStyle = this.color;
                ctx.font = `${this.size}px Arial`;
                ctx.textAlign = 'center';
                ctx.fillText(this.text, this.x, this.y);
                ctx.restore();
            }
        };
        
        this.particles.push(particle);
    }

    // Create death particles
    createDeathParticles() {
        for (let i = 0; i < 8; i++) {
            const particle = {
                x: this.x,
                y: this.y,
                vx: Utils.random(-3, 3),
                vy: Utils.random(-3, 3),
                life: 1.0,
                maxLife: 1.0,
                color: this.color,
                size: Utils.random(2, 6),
                update: function(deltaTime) {
                    this.x += this.vx;
                    this.y += this.vy;
                    this.vy += 0.1; // Gravity
                    this.life -= deltaTime;
                    
                    if (this.life <= 0) {
                        this.dead = true;
                    }
                },
                render: function(ctx) {
                    const alpha = this.life / this.maxLife;
                    ctx.save();
                    ctx.globalAlpha = alpha;
                    ctx.fillStyle = this.color;
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.restore();
                }
            };
            
            this.particles.push(particle);
        }
    }

    // Set animation speed
    setAnimationSpeed(speed) {
        this.animationSpeed = speed;
    }

    // Set color
    setColor(color) {
        this.color = color;
    }

    // Set opacity
    setOpacity(opacity) {
        this.opacity = Utils.clamp(opacity, 0, 1);
    }

    // Set scale
    setScale(scale) {
        this.scale = scale;
    }

    // Set rotation
    setRotation(rotation) {
        this.rotation = rotation;
    }

    // Add tag
    addTag(tag) {
        if (!this.tags.includes(tag)) {
            this.tags.push(tag);
        }
    }

    // Remove tag
    removeTag(tag) {
        const index = this.tags.indexOf(tag);
        if (index > -1) {
            this.tags.splice(index, 1);
        }
    }

    // Check if entity has tag
    hasTag(tag) {
        return this.tags.includes(tag);
    }

    // Get health percentage
    getHealthPercentage() {
        return this.health / this.maxHealth;
    }

    // Check if entity is alive
    isAlive() {
        return !this.dead && this.health > 0;
    }

    // Check if entity is moving
    isMoving() {
        return Math.abs(this.vx) > 0.1 || Math.abs(this.vy) > 0.1;
    }

    // Get entity bounds
    getBounds() {
        return {
            x: this.x - this.width / 2,
            y: this.y - this.height / 2,
            width: this.width,
            height: this.height
        };
    }

    // Virtual methods (to be overridden)
    onDeath() {
        // Override in subclasses
    }

    onDamage(amount, source) {
        // Override in subclasses
    }

    onHeal(amount) {
        // Override in subclasses
    }

    onCollision(entity) {
        // Override in subclasses
    }

    // Render entity
    render(ctx, camera) {
        if (!this.visible) return;
        
        // Apply camera transform
        const screenX = (this.x - camera.x) * camera.zoom + camera.width / 2;
        const screenY = (this.y - camera.y) * camera.zoom + camera.height / 2;
        
        // Check if entity is on screen
        if (screenX < -this.width || screenX > camera.width + this.width ||
            screenY < -this.height || screenY > camera.height + this.height) {
            return;
        }
        
        ctx.save();
        
        // Apply entity transforms
        ctx.translate(screenX, screenY);
        ctx.rotate(this.rotation);
        ctx.scale(this.scale * camera.zoom, this.scale * camera.zoom);
        ctx.globalAlpha = this.opacity;
        
        // Render entity
        this.renderEntity(ctx);
        
        // Render particles
        this.renderParticles(ctx, camera);
        
        // Render effects
        this.renderEffects(ctx, camera);
        
        ctx.restore();
    }

    // Render the entity itself (to be overridden)
    renderEntity(ctx) {
        // Default rendering - colored circle
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.width / 2, 0, Math.PI * 2);
        ctx.fill();
        
        // Health bar
        if (this.health < this.maxHealth) {
            const barWidth = this.width;
            const barHeight = 4;
            const barY = -this.height / 2 - 10;
            
            // Background
            ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.fillRect(-barWidth / 2, barY, barWidth, barHeight);
            
            // Health fill
            const healthPercent = this.health / this.maxHealth;
            ctx.fillStyle = healthPercent > 0.5 ? '#44ff44' : healthPercent > 0.25 ? '#ffaa44' : '#ff4444';
            ctx.fillRect(-barWidth / 2, barY, barWidth * healthPercent, barHeight);
        }
    }

    // Render particles
    renderParticles(ctx, camera) {
        this.particles.forEach(particle => {
            if (particle.render) {
                particle.render(ctx);
            }
        });
    }

    // Render effects
    renderEffects(ctx, camera) {
        this.effects.forEach(effect => {
            if (effect.render) {
                effect.render(ctx, camera);
            }
        });
    }

    // Clone entity
    clone() {
        const cloned = new Entity(this.x, this.y, this.type, {
            width: this.width,
            height: this.height,
            health: this.health,
            maxHealth: this.maxHealth,
            speed: this.speed,
            damage: this.damage,
            defense: this.defense,
            color: this.color,
            collisionRadius: this.collisionRadius,
            solid: this.solid,
            renderLayer: this.renderLayer,
            tags: [...this.tags]
        });
        
        return cloned;
    }

    // Serialize entity for network transmission
    serialize() {
        return {
            id: this.id,
            type: this.type,
            x: this.x,
            y: this.y,
            health: this.health,
            maxHealth: this.maxHealth,
            vx: this.vx,
            vy: this.vy,
            rotation: this.rotation,
            scale: this.scale,
            opacity: this.opacity,
            dead: this.dead,
            active: this.active,
            tags: this.tags
        };
    }

    // Deserialize entity from network data
    deserialize(data) {
        this.x = data.x;
        this.y = data.y;
        this.health = data.health;
        this.maxHealth = data.maxHealth;
        this.vx = data.vx;
        this.vy = data.vy;
        this.rotation = data.rotation;
        this.scale = data.scale;
        this.opacity = data.opacity;
        this.dead = data.dead;
        this.active = data.active;
        this.tags = data.tags || [];
    }
} 